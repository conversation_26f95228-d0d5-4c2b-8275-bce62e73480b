# 🚀 启动指南

## 问题解决

### 原始问题
用户在启动FastAPI应用时遇到了模块导入错误：
```
ModuleNotFoundError: No module named 'api'
```

### 问题原因
1. **导入路径错误**：代码中使用了 `from api.dependencies` 等相对导入，但实际的模块路径是 `src.api.dependencies`
2. **Python路径设置**：uvicorn启动时，Python路径没有正确包含项目的src目录
3. **工作目录问题**：启动命令的工作目录与代码期望的路径不匹配

### 解决方案
1. **修复所有导入路径**：将所有相对导入改为绝对导入
   - `from api.dependencies` → `from src.api.dependencies`
   - `from models.base_model` → `from src.models.base_model`
   - 等等...

2. **创建启动脚本**：`start_server.py` 自动处理路径问题
3. **语法错误修复**：修复了 `global` 变量声明的位置问题

## 🛠️ 新增文件

### 1. `start_server.py` - 主启动脚本
**功能**：
- 自动设置Python路径
- 检查项目依赖和结构
- 支持开发模式和生产模式
- 提供详细的启动日志
- 创建必要的目录

**使用方法**：
```bash
python start_server.py --dev     # 开发模式
python start_server.py           # 生产模式
python start_server.py --port 8080  # 指定端口
python start_server.py --skip-checks  # 跳过检查
```

### 2. `quick_start.py` - 一键启动脚本
**功能**：
- 检查Python版本和项目结构
- 自动启动服务器
- 自动测试API功能
- 显示访问地址
- 支持Ctrl+C优雅停止

**使用方法**：
```bash
python quick_start.py
```

### 3. `test_api.py` - API测试脚本
**功能**：
- 测试所有主要API端点
- 检查服务器健康状态
- 等待服务器启动
- 显示详细测试结果

**使用方法**：
```bash
python test_api.py                    # 测试本地API
python test_api.py --url http://localhost:8080  # 测试指定地址
python test_api.py --wait             # 等待服务器启动
```

## 📝 修复的文件

### 导入路径修复
以下文件的导入路径已修复：

1. **`src/api/main.py`**
   - `from api.dependencies` → `from src.api.dependencies`
   - `from api.routers` → `from src.api.routers`

2. **`src/api/routers/prediction.py`**
   - 所有 `from api.*` → `from src.api.*`

3. **`src/api/routers/adaptive.py`**
   - 所有 `from api.*` → `from src.api.*`
   - 修复了 `global` 变量声明位置

4. **`src/api/routers/comparison.py`**
   - 所有 `from api.*` → `from src.api.*`

5. **`src/api/routers/dataset.py`**
   - 所有 `from api.*` → `from src.api.*`

6. **`src/api/core/model_manager.py`**
   - `from models.model_factory` → `from src.models.base_model` 和 `from src.optimization.distillation`
   - `from api.dependencies` → `from src.api.dependencies`

7. **`src/api/core/adaptive_tuning.py`**
   - 所有相关导入路径修复

8. **`src/api/core/image_processor.py`**
   - `from api.*` → `from src.api.*`

9. **`src/api/core/comparison_generator.py`**
   - `from api.dependencies` → `from src.api.dependencies`

10. **`src/optimization/distillation.py`**
    - `from models.base_model` → `from src.models.base_model`

## 🎯 使用建议

### 新手用户
```bash
# 最简单的方式
python quick_start.py
```

### 开发者
```bash
# 开发模式（推荐）
python start_server.py --dev

# 或者传统方式
python -m uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --reload
```

### 生产环境
```bash
# 生产模式
python start_server.py --workers 4

# 或者使用Docker
docker build -t yaogan-classification .
docker run -p 8000:8000 yaogan-classification
```

## 🔍 验证安装

启动后访问以下地址验证：

1. **主页面**: http://localhost:8000/static/index.html
2. **API文档**: http://localhost:8000/docs
3. **健康检查**: http://localhost:8000/health

或者运行测试脚本：
```bash
python test_api.py
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   python start_server.py --port 8080
   ```

2. **依赖缺失**
   ```bash
   pip install -r requirements.txt
   ```

3. **权限问题**
   ```bash
   # 确保有读写权限
   chmod +x start_server.py
   ```

4. **Python版本**
   ```bash
   # 确保使用Python 3.8+
   python --version
   ```

### 调试模式
```bash
# 跳过所有检查，快速启动
python start_server.py --skip-checks --dev
```

## ✅ 验证修复

所有导入错误已修复，现在可以正常启动：

```bash
✅ 导入测试通过
✅ 服务器启动成功
✅ API功能正常
✅ 前端页面可访问
```

项目现在可以稳定运行，支持所有原有功能！
