/* 遥感场景分类项目 - 组件样式文件 */
/* 专门的组件样式，包括表格、卡片、模态框等 */

/* 表格样式 */
.table-container {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    border: 1px solid var(--gray-200);
    margin: var(--spacing-lg) 0;
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.table th {
    background: var(--gray-100);
    color: var(--gray-800);
    font-weight: 600;
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: left;
    border-bottom: 2px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table td {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-700);
}

.table tbody tr:hover {
    background: var(--gray-50);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* 表格响应式 */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
    .table th,
    .table td {
        padding: var(--spacing-sm);
        font-size: 0.8rem;
    }
}

/* 卡片组件 */
.card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition-fast);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: var(--gray-100);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.card-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background: var(--gray-50);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

/* 徽章组件 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: var(--radius-sm);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-primary {
    background: var(--primary-light);
    color: var(--primary-color);
}

.badge-success {
    background: #dcfce7;
    color: var(--success-color);
}

.badge-warning {
    background: #fef3c7;
    color: var(--warning-color);
}

.badge-error {
    background: #fee2e2;
    color: var(--error-color);
}

.badge-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
}

/* 进度条组件 */
.progress {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin: var(--spacing-md) 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* 提示框组件 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background: var(--gray-800);
    color: white;
    text-align: center;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity var(--transition-fast);
    font-size: 0.8rem;
}

.tooltip .tooltip-text::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--gray-800) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* 模态框组件 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    padding: var(--spacing-lg);
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn var(--transition-normal);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-footer {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

/* 下拉菜单组件 */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background: white;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-fast);
}

.dropdown-toggle:hover {
    border-color: var(--primary-color);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    display: none;
    padding: var(--spacing-sm) 0;
    margin-top: var(--spacing-xs);
}

.dropdown.show .dropdown-menu {
    display: block;
    animation: dropdownSlideIn var(--transition-fast);
}

@keyframes dropdownSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-lg);
    color: var(--gray-700);
    text-decoration: none;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.dropdown-item:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--spacing-sm) 0;
}

/* 分页组件 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
    margin: var(--spacing-xl) 0;
}

.pagination-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: white;
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-fast);
}

.pagination-item:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.pagination-item.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.pagination-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}
