#!/usr/bin/env python3
"""
快速启动和测试脚本

一键启动服务器并测试API功能
"""

import subprocess
import time
import sys
import os
from pathlib import Path

def main():
    """主函数"""
    print("🛰️ 遥感图像分类项目 - 快速启动")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查项目结构
    project_root = Path(__file__).parent
    required_files = [
        "start_server.py",
        "test_api.py", 
        "src/api/main.py",
        "requirements.txt"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (project_root / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 项目结构检查通过")
    
    # 启动服务器
    print("\n🚀 启动FastAPI服务器...")
    try:
        # 使用启动脚本启动服务器
        server_process = subprocess.Popen([
            sys.executable, "start_server.py", 
            "--skip-checks", "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("⏳ 等待服务器启动...")
        time.sleep(4)  # 等待服务器启动
        
        # 检查服务器是否还在运行
        if server_process.poll() is not None:
            stdout, stderr = server_process.communicate()
            print(f"❌ 服务器启动失败")
            print(f"错误输出: {stderr.decode()}")
            return False
        
        print("✅ 服务器启动成功")
        
        # 测试API
        # print("\n🧪 测试API功能...")
        # test_process = subprocess.run([
        #     sys.executable, "test_api.py", "--wait"
        # ], capture_output=True, text=True)
        
        # if test_process.returncode == 0:
        #     print("✅ API测试通过")
        #     print(test_process.stdout)
        # else:
        #     print("❌ API测试失败")
        #     print(test_process.stderr)
        
        # 显示访问信息
        print("\n🌐 服务访问地址:")
        print("  主页面: http://localhost:8000/static/index.html")
        print("  API文档: http://localhost:8000/docs")
        print("  健康检查: http://localhost:8000/health")
        
        print("\n⚡ 服务器正在运行中...")
        print("按 Ctrl+C 停止服务器")
        
        # 等待用户中断
        try:
            server_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            server_process.terminate()
            server_process.wait()
            print("✅ 服务器已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 启动失败，请检查错误信息")
        print("\n💡 故障排除建议:")
        print("1. 确保已安装依赖: pip install -r requirements.txt")
        print("2. 检查端口8000是否被占用")
        print("3. 手动启动: python start_server.py --dev")
        sys.exit(1)
    else:
        print("\n✅ 项目启动成功！")
