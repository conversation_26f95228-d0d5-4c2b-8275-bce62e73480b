<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>遥感场景分类模型评估与比较</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <header class="header">
            <h1><i class="fas fa-satellite"></i> 遥感场景分类模型评估与比较</h1>
            <p class="subtitle">Remote Sensing Scene Classification Model Evaluation and Comparison</p>
            <div class="status-bar">
                <span class="status-item">
                    <i class="fas fa-folder"></i>
                    <span>当前模型目录: </span>
                    <span id="current-model-dir">加载中...</span>
                </span>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 标签页导航 -->
            <nav class="tab-nav">
                <button class="tab-btn active" data-tab="adaptive-tuning">
                    <i class="fas fa-cogs"></i>
                    自适应微调控制
                </button>
                <button class="tab-btn" data-tab="model-prediction">
                    <i class="fas fa-brain"></i>
                    模型预测
                </button>
                <button class="tab-btn" data-tab="densenet-eval">
                    <i class="fas fa-chart-line"></i>
                    DenseNet201评估
                </button>
                <button class="tab-btn" data-tab="resnet-eval">
                    <i class="fas fa-chart-bar"></i>
                    ResNet50评估
                </button>
                <button class="tab-btn" data-tab="vit-eval">
                    <i class="fas fa-eye"></i>
                    ViT-S/16评估
                </button>
                <button class="tab-btn" data-tab="swin-eval">
                    <i class="fas fa-window-maximize"></i>
                    Swin-T评估
                </button>
            </nav>

            <!-- 标签页内容 -->
            <div class="tab-content">
                <!-- 自适应微调控制标签页 -->
                <div class="tab-pane active" id="adaptive-tuning">
                    <div class="section-header">
                        <h2><i class="fas fa-cogs"></i> 自适应微调控制面板</h2>
                        <p>监控new_dataset目录下的新数据，当检测到数据分布差异超过阈值时自动触发微调</p>
                    </div>

                    <div class="control-panel">
                        <div class="control-section">
                            <div class="control-group">
                                <h3><i class="fas fa-play-circle"></i> 自动监控控制</h3>
                                <div class="button-group">
                                    <button id="start-monitor-btn" class="btn btn-primary">
                                        <i class="fas fa-play"></i>
                                        启动自适应监控
                                    </button>
                                    <button id="stop-monitor-btn" class="btn btn-secondary">
                                        <i class="fas fa-stop"></i>
                                        停止自适应监控
                                    </button>
                                </div>
                                <div class="status-display">
                                    <label for="monitor-status">监控状态</label>
                                    <textarea id="monitor-status" readonly>未启动</textarea>
                                </div>
                            </div>

                            <div class="control-group">
                                <h3><i class="fas fa-hand-paper"></i> 手动微调控制</h3>
                                <div class="button-group">
                                    <button id="manual-tune-btn" class="btn btn-secondary">
                                        <i class="fas fa-wrench"></i>
                                        手动触发微调
                                    </button>
                                    <button id="check-distribution-btn" class="btn btn-secondary">
                                        <i class="fas fa-search"></i>
                                        检查数据分布
                                    </button>
                                    <button id="refresh-status-btn" class="btn btn-secondary">
                                        <i class="fas fa-sync"></i>
                                        刷新微调状态
                                    </button>
                                    <button id="refresh-images-btn" class="btn btn-secondary">
                                        <i class="fas fa-images"></i>
                                        刷新数据集对比
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="status-section">
                            <div class="status-group">
                                <h3><i class="fas fa-info-circle"></i> 微调状态</h3>
                                <textarea id="fine-tune-status" readonly>待命中</textarea>
                            </div>

                            <div class="config-group">
                                <h3><i class="fas fa-sliders-h"></i> 配置参数</h3>
                                <div class="slider-group">
                                    <label for="threshold-slider">分布差异阈值</label>
                                    <input type="range" id="threshold-slider" min="0.01" max="0.5" step="0.01" value="0.1">
                                    <span id="threshold-value">0.1</span>
                                </div>
                                <div class="status-display">
                                    <label for="distribution-info">分布检查结果</label>
                                    <textarea id="distribution-info" readonly>未检查</textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据集图片对比展示 -->
                    <div class="dataset-comparison">
                        <div class="section-header">
                            <h2><i class="fas fa-images"></i> 数据集图片对比</h2>
                            <p>展示原始数据集和新数据集中同一类别的4张示例图片，帮助理解数据分布差异</p>
                            <div class="tip">
                                <i class="fas fa-lightbulb"></i>
                                <strong>提示:</strong> 点击'刷新数据集对比'按钮可以随机选择新的类别和图片进行对比
                            </div>
                        </div>

                        <div class="comparison-container">
                            <div class="dataset-section">
                                <h3><i class="fas fa-database"></i> 原始数据集 (old_dataset)</h3>
                                <div class="image-gallery" id="old-gallery">
                                    <!-- 图片将通过JavaScript动态加载 -->
                                </div>
                                <div class="image-info">
                                    <label for="old-images-info">原始数据集图片信息</label>
                                    <textarea id="old-images-info" readonly>无图片数据</textarea>
                                </div>
                            </div>

                            <div class="dataset-section">
                                <h3><i class="fas fa-plus-circle"></i> 新数据集 (new_dataset)</h3>
                                <div class="image-gallery" id="new-gallery">
                                    <!-- 图片将通过JavaScript动态加载 -->
                                </div>
                                <div class="image-info">
                                    <label for="new-images-info">新数据集图片信息</label>
                                    <textarea id="new-images-info" readonly>无图片数据</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模型预测标签页 -->
                <div class="tab-pane" id="model-prediction">
                    <div class="section-header">
                        <h2><i class="fas fa-brain"></i> 模型预测</h2>
                        <p>上传图像，选择模型进行预测</p>
                    </div>

                    <div class="prediction-container">
                        <div class="input-section">
                            <div class="upload-area">
                                <h3><i class="fas fa-upload"></i> 上传图像</h3>
                                <div class="file-upload">
                                    <input type="file" id="image-input" accept="image/*">
                                    <label for="image-input" class="upload-label">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <span>点击选择图像文件</span>
                                    </label>
                                    <div class="image-preview" id="image-preview">
                                        <!-- 预览图片将显示在这里 -->
                                    </div>
                                </div>
                            </div>

                            <div class="model-selection">
                                <h3><i class="fas fa-list"></i> 选择模型</h3>
                                <select id="model-select">
                                    <option value="">请选择模型...</option>
                                    <!-- 模型选项将通过JavaScript动态加载 -->
                                </select>
                                <button id="predict-btn" class="btn btn-primary" disabled>
                                    <i class="fas fa-magic"></i>
                                    开始预测
                                </button>
                            </div>
                        </div>

                        <div class="output-section">
                            <div class="prediction-results">
                                <h3><i class="fas fa-chart-pie"></i> 预测结果</h3>
                                <div id="prediction-output" class="result-display">
                                    <div class="no-result">
                                        <i class="fas fa-info-circle"></i>
                                        <span>请上传图像并选择模型进行预测</span>
                                    </div>
                                </div>
                            </div>

                            <div class="inference-time">
                                <h3><i class="fas fa-clock"></i> 推理时间</h3>
                                <div id="time-output" class="time-display">
                                    <span>--</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- DenseNet201评估标签页 -->
                <div class="tab-pane" id="densenet-eval">
                    <div class="section-header">
                        <h2><i class="fas fa-chart-line"></i> DenseNet201 模型评估</h2>
                        <p>DenseNet201模型的性能评估和比较分析</p>
                    </div>

                    <div class="eval-tabs">
                        <div class="eval-tab-nav">
                            <button class="eval-tab-btn active" data-eval-tab="densenet-pruning">
                                <i class="fas fa-cut"></i>
                                剪枝比较
                            </button>
                            <button class="eval-tab-btn" data-eval-tab="densenet-distillation">
                                <i class="fas fa-flask"></i>
                                蒸馏比较
                            </button>
                        </div>

                        <div class="eval-tab-content">
                            <div class="eval-tab-pane active" id="densenet-pruning">
                                <div id="densenet-pruning-results" class="evaluation-results">
                                    <!-- 剪枝比较结果将通过JavaScript动态加载 -->
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>加载剪枝比较结果...</span>
                                    </div>
                                </div>
                            </div>

                            <div class="eval-tab-pane" id="densenet-distillation">
                                <div id="densenet-distillation-results" class="evaluation-results">
                                    <!-- 蒸馏比较结果将通过JavaScript动态加载 -->
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>加载蒸馏比较结果...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ResNet50评估标签页 -->
                <div class="tab-pane" id="resnet-eval">
                    <div class="section-header">
                        <h2><i class="fas fa-chart-bar"></i> ResNet50 模型评估</h2>
                        <p>ResNet50模型的性能评估和比较分析</p>
                    </div>

                    <div class="eval-tabs">
                        <div class="eval-tab-nav">
                            <button class="eval-tab-btn active" data-eval-tab="resnet-pruning">
                                <i class="fas fa-cut"></i>
                                剪枝比较
                            </button>
                            <button class="eval-tab-btn" data-eval-tab="resnet-distillation">
                                <i class="fas fa-flask"></i>
                                蒸馏比较
                            </button>
                            <button class="eval-tab-btn" data-eval-tab="resnet-quantization">
                                <i class="fas fa-compress"></i>
                                量化比较
                            </button>
                        </div>

                        <div class="eval-tab-content">
                            <div class="eval-tab-pane active" id="resnet-pruning">
                                <div id="resnet-pruning-results" class="evaluation-results">
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>加载剪枝比较结果...</span>
                                    </div>
                                </div>
                            </div>

                            <div class="eval-tab-pane" id="resnet-distillation">
                                <div id="resnet-distillation-results" class="evaluation-results">
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>加载蒸馏比较结果...</span>
                                    </div>
                                </div>
                            </div>

                            <div class="eval-tab-pane" id="resnet-quantization">
                                <div id="resnet-quantization-results" class="evaluation-results">
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>加载量化比较结果...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ViT-S/16评估标签页 -->
                <div class="tab-pane" id="vit-eval">
                    <div class="section-header">
                        <h2><i class="fas fa-eye"></i> ViT-S/16 模型评估</h2>
                        <p>Vision Transformer Small模型的性能评估和比较分析</p>
                    </div>

                    <div class="eval-tabs">
                        <div class="eval-tab-nav">
                            <button class="eval-tab-btn active" data-eval-tab="vit-pruning">
                                <i class="fas fa-cut"></i>
                                剪枝比较
                            </button>
                            <button class="eval-tab-btn" data-eval-tab="vit-distillation">
                                <i class="fas fa-flask"></i>
                                蒸馏比较
                            </button>
                        </div>

                        <div class="eval-tab-content">
                            <div class="eval-tab-pane active" id="vit-pruning">
                                <div id="vit-pruning-results" class="evaluation-results">
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>加载剪枝比较结果...</span>
                                    </div>
                                </div>
                            </div>

                            <div class="eval-tab-pane" id="vit-distillation">
                                <div id="vit-distillation-results" class="evaluation-results">
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>加载蒸馏比较结果...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Swin-T评估标签页 -->
                <div class="tab-pane" id="swin-eval">
                    <div class="section-header">
                        <h2><i class="fas fa-window-maximize"></i> Swin-T 模型评估</h2>
                        <p>Swin Transformer Tiny模型的性能评估和比较分析</p>
                    </div>

                    <div class="eval-tabs">
                        <div class="eval-tab-nav">
                            <button class="eval-tab-btn active" data-eval-tab="swin-pruning">
                                <i class="fas fa-cut"></i>
                                剪枝比较
                            </button>
                            <button class="eval-tab-btn" data-eval-tab="swin-distillation">
                                <i class="fas fa-flask"></i>
                                蒸馏比较
                            </button>
                        </div>

                        <div class="eval-tab-content">
                            <div class="eval-tab-pane active" id="swin-pruning">
                                <div id="swin-pruning-results" class="evaluation-results">
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>加载剪枝比较结果...</span>
                                    </div>
                                </div>
                            </div>

                            <div class="eval-tab-pane" id="swin-distillation">
                                <div id="swin-distillation-results" class="evaluation-results">
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>加载蒸馏比较结果...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <p>&copy; 2024 遥感场景分类项目. All rights reserved.</p>
        </footer>
    </div>

    <!-- 加载提示 -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>处理中...</span>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/api.js"></script>
    <script src="js/prediction.js"></script>
    <script src="js/adaptive.js"></script>
    <script src="js/comparison.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
