"""
通用响应数据模型

定义API的通用响应格式，包括基础响应、成功响应、错误响应等。
确保所有API端点使用统一的响应格式。
"""

from typing import Any, Optional, Dict, Union
from pydantic import BaseModel, Field
from datetime import datetime


class BaseResponse(BaseModel):
    """基础响应模型"""
    
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SuccessResponse(BaseResponse):
    """成功响应模型"""
    
    success: bool = Field(default=True, description="请求成功标志")
    data: Optional[Any] = Field(default=None, description="响应数据")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "操作成功",
                "data": {"result": "示例数据"},
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    
    success: bool = Field(default=False, description="请求失败标志")
    error_code: Optional[str] = Field(default=None, description="错误代码")
    error_details: Optional[Dict[str, Any]] = Field(default=None, description="错误详情")
    
    class Config:
        schema_extra = {
            "example": {
                "success": False,
                "message": "操作失败",
                "error_code": "VALIDATION_ERROR",
                "error_details": {"field": "参数验证失败"},
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class StatusResponse(BaseResponse):
    """状态响应模型"""
    
    status: str = Field(..., description="当前状态")
    progress: Optional[float] = Field(default=None, ge=0, le=100, description="进度百分比")
    details: Optional[Dict[str, Any]] = Field(default=None, description="状态详情")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "状态正常",
                "status": "running",
                "progress": 75.5,
                "details": {"current_step": "数据处理中"},
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    
    status: str = Field(..., description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    version: str = Field(default="1.0.0", description="API版本")
    uptime: Optional[str] = Field(default=None, description="运行时间")
    dependencies: Optional[Dict[str, str]] = Field(default=None, description="依赖服务状态")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "healthy",
                "timestamp": "2023-12-01T12:00:00",
                "version": "1.0.0",
                "uptime": "2 days, 3 hours",
                "dependencies": {
                    "database": "connected",
                    "storage": "available"
                }
            }
        }


class PaginationMeta(BaseModel):
    """分页元数据模型"""
    
    page: int = Field(..., ge=1, description="当前页码")
    page_size: int = Field(..., ge=1, le=100, description="每页大小")
    total: int = Field(..., ge=0, description="总记录数")
    total_pages: int = Field(..., ge=0, description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")
    
    class Config:
        schema_extra = {
            "example": {
                "page": 1,
                "page_size": 10,
                "total": 100,
                "total_pages": 10,
                "has_next": True,
                "has_prev": False
            }
        }


class PaginatedResponse(SuccessResponse):
    """分页响应模型"""
    
    meta: PaginationMeta = Field(..., description="分页元数据")
    items: list = Field(..., description="数据项列表")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取数据成功",
                "data": None,
                "meta": {
                    "page": 1,
                    "page_size": 10,
                    "total": 100,
                    "total_pages": 10,
                    "has_next": True,
                    "has_prev": False
                },
                "items": [{"id": 1, "name": "示例项目"}],
                "timestamp": "2023-12-01T12:00:00"
            }
        }
