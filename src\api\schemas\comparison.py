"""
模型比较数据模型

定义模型比较相关API的请求和响应数据模型，包括剪枝比较、蒸馏比较、量化比较等。
与原有比较函数的返回格式保持一致。
"""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator

from .common import SuccessResponse


class ModelMetrics(BaseModel):
    """模型评估指标模型"""
    
    accuracy: float = Field(..., ge=0, le=1, description="准确率")
    precision: float = Field(..., ge=0, le=1, description="精确率")
    recall: float = Field(..., ge=0, le=1, description="召回率")
    f1: float = Field(..., ge=0, le=1, description="F1分数")
    parameter_count: Optional[int] = Field(default=None, ge=0, description="参数数量")
    size_mb: Optional[float] = Field(default=None, ge=0, description="模型大小(MB)")
    inference_time_ms: Optional[float] = Field(default=None, ge=0, description="推理时间(毫秒)")
    
    class Config:
        schema_extra = {
            "example": {
                "accuracy": 0.8542,
                "precision": 0.8456,
                "recall": 0.8398,
                "f1": 0.8427,
                "parameter_count": 25000000,
                "size_mb": 97.8,
                "inference_time_ms": 23.45
            }
        }


class ModelComparisonItem(BaseModel):
    """模型比较项模型"""
    
    model_name: str = Field(..., description="模型名称")
    model_type: str = Field(..., description="模型类型")
    variant: str = Field(..., description="模型变体")
    metrics: ModelMetrics = Field(..., description="评估指标")
    additional_info: Optional[Dict[str, Any]] = Field(default=None, description="额外信息")
    
    class Config:
        schema_extra = {
            "example": {
                "model_name": "ResNet50 原始模型",
                "model_type": "resnet50",
                "variant": "original",
                "metrics": {
                    "accuracy": 0.8542,
                    "precision": 0.8456,
                    "recall": 0.8398,
                    "f1": 0.8427,
                    "parameter_count": 25000000,
                    "size_mb": 97.8
                },
                "additional_info": {
                    "training_time": "2 hours",
                    "dataset": "UCMerced"
                }
            }
        }


class ComparisonTable(BaseModel):
    """比较表格模型"""
    
    title: str = Field(..., description="表格标题")
    html_content: str = Field(..., description="HTML格式的表格内容")
    models: List[ModelComparisonItem] = Field(..., description="参与比较的模型列表")
    comparison_type: str = Field(..., description="比较类型 (pruning, distillation, quantization)")
    
    class Config:
        schema_extra = {
            "example": {
                "title": "ResNet50 剪枝模型与原始模型比较",
                "html_content": "<table class='pure-table'>...</table>",
                "models": [
                    {
                        "model_name": "ResNet50 原始模型",
                        "model_type": "resnet50",
                        "variant": "original",
                        "metrics": {"accuracy": 0.8542}
                    }
                ],
                "comparison_type": "pruning"
            }
        }


class ComparisonResponse(SuccessResponse):
    """通用比较响应模型"""
    
    comparison: ComparisonTable = Field(..., description="比较结果")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "比较生成成功",
                "comparison": {
                    "title": "模型比较结果",
                    "html_content": "<table>...</table>",
                    "models": [],
                    "comparison_type": "pruning"
                },
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class PruningComparisonRequest(BaseModel):
    """剪枝比较请求模型"""
    
    model_type: str = Field(..., description="要比较的模型类型")
    
    @validator('model_type')
    def validate_model_type(cls, v):
        allowed_types = ["densenet201", "resnet50", "swin_t", "vit_s_16"]
        if v not in allowed_types:
            raise ValueError(f'模型类型必须是以下之一: {allowed_types}')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "model_type": "resnet50"
            }
        }


class PruningComparisonResponse(ComparisonResponse):
    """剪枝比较响应模型"""
    
    sparsity_info: Optional[Dict[str, Any]] = Field(default=None, description="稀疏度信息")
    parameter_reduction: Optional[Dict[str, float]] = Field(default=None, description="参数减少信息")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "剪枝比较生成成功",
                "comparison": {
                    "title": "ResNet50 剪枝模型与原始模型比较",
                    "html_content": "<table>...</table>",
                    "models": [],
                    "comparison_type": "pruning"
                },
                "sparsity_info": {
                    "original_sparsity": 0.0,
                    "pruned_sparsity": 0.75
                },
                "parameter_reduction": {
                    "original_params": 25000000,
                    "pruned_params": 6250000,
                    "reduction_ratio": 0.75
                },
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class DistillationComparisonRequest(BaseModel):
    """蒸馏比较请求模型"""
    
    model_type: str = Field(..., description="要比较的模型类型")
    
    @validator('model_type')
    def validate_model_type(cls, v):
        allowed_types = ["densenet201", "resnet50", "swin_t", "vit_s_16"]
        if v not in allowed_types:
            raise ValueError(f'模型类型必须是以下之一: {allowed_types}')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "model_type": "resnet50"
            }
        }


class DistillationComparisonResponse(ComparisonResponse):
    """蒸馏比较响应模型"""
    
    compression_ratio: Optional[float] = Field(default=None, description="压缩比")
    knowledge_transfer_efficiency: Optional[float] = Field(default=None, description="知识迁移效率")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "蒸馏比较生成成功",
                "comparison": {
                    "title": "ResNet50 蒸馏模型与原始模型比较",
                    "html_content": "<table>...</table>",
                    "models": [],
                    "comparison_type": "distillation"
                },
                "compression_ratio": 7.35,
                "knowledge_transfer_efficiency": 0.92,
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class QuantizationComparisonRequest(BaseModel):
    """量化比较请求模型"""
    
    model_type: str = Field(default="resnet50", description="要比较的模型类型，目前仅支持resnet50")
    
    @validator('model_type')
    def validate_model_type(cls, v):
        if v != "resnet50":
            raise ValueError('量化比较目前仅支持resnet50')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "model_type": "resnet50"
            }
        }


class QuantizationComparisonResponse(ComparisonResponse):
    """量化比较响应模型"""
    
    quantization_type: Optional[str] = Field(default=None, description="量化类型")
    size_reduction: Optional[Dict[str, float]] = Field(default=None, description="大小减少信息")
    speed_improvement: Optional[float] = Field(default=None, description="速度提升倍数")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "量化比较生成成功",
                "comparison": {
                    "title": "ResNet50 量化模型与原始模型比较",
                    "html_content": "<table>...</table>",
                    "models": [],
                    "comparison_type": "quantization"
                },
                "quantization_type": "INT8",
                "size_reduction": {
                    "original_size_mb": 97.8,
                    "quantized_size_mb": 24.5,
                    "reduction_ratio": 0.75
                },
                "speed_improvement": 2.3,
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class ComparisonOverviewResponse(SuccessResponse):
    """比较概览响应模型"""
    
    available_comparisons: List[str] = Field(..., description="可用的比较类型")
    model_types: List[str] = Field(..., description="支持的模型类型")
    comparison_summaries: Dict[str, Dict[str, Any]] = Field(..., description="比较摘要信息")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取比较概览成功",
                "available_comparisons": ["pruning", "distillation", "quantization"],
                "model_types": ["densenet201", "resnet50", "swin_t", "vit_s_16"],
                "comparison_summaries": {
                    "pruning": {
                        "description": "模型剪枝比较",
                        "supported_models": ["densenet201", "resnet50", "swin_t", "vit_s_16"]
                    },
                    "distillation": {
                        "description": "知识蒸馏比较",
                        "supported_models": ["densenet201", "resnet50", "swin_t", "vit_s_16"]
                    },
                    "quantization": {
                        "description": "模型量化比较",
                        "supported_models": ["resnet50"]
                    }
                },
                "timestamp": "2023-12-01T12:00:00"
            }
        }
