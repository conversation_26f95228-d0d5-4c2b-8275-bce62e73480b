"""
预测相关数据模型

定义模型预测API的请求和响应数据模型，包括图像上传、模型选择、预测结果等。
与原有predict函数的返回格式保持一致。
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, validator
from fastapi import UploadFile

from .common import SuccessResponse, BaseResponse


class ModelInfo(BaseModel):
    """模型信息模型"""
    
    key: str = Field(..., description="模型键名，格式为 model_type-variant")
    name: str = Field(..., description="模型显示名称")
    type: str = Field(..., description="模型类型 (densenet201, resnet50, swin_t, vit_s_16)")
    variant: str = Field(..., description="模型变体 (original, pruned, distilled, quantized)")
    device: str = Field(..., description="运行设备 (cuda, cpu)")
    size_mb: Optional[float] = Field(default=None, description="模型文件大小(MB)")
    metrics: Optional[Dict[str, float]] = Field(default=None, description="模型评估指标")
    
    class Config:
        schema_extra = {
            "example": {
                "key": "resnet50-原始",
                "name": "ResNet50 原始模型",
                "type": "resnet50",
                "variant": "original",
                "device": "cuda",
                "size_mb": 97.8,
                "metrics": {
                    "accuracy": 0.8542,
                    "precision": 0.8456,
                    "recall": 0.8398,
                    "f1": 0.8427
                }
            }
        }


class ModelListResponse(SuccessResponse):
    """模型列表响应模型"""
    
    models: List[ModelInfo] = Field(..., description="可用模型列表")
    total_count: int = Field(..., description="模型总数")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取模型列表成功",
                "models": [
                    {
                        "key": "resnet50-原始",
                        "name": "ResNet50 原始模型",
                        "type": "resnet50",
                        "variant": "original",
                        "device": "cuda",
                        "size_mb": 97.8,
                        "metrics": {"accuracy": 0.8542}
                    }
                ],
                "total_count": 1,
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class PredictionRequest(BaseModel):
    """预测请求模型"""
    
    model_key: str = Field(..., description="要使用的模型键名")
    image_data: Optional[str] = Field(default=None, description="Base64编码的图像数据")
    
    @validator('model_key')
    def validate_model_key(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('模型键名不能为空')
        return v.strip()
    
    class Config:
        schema_extra = {
            "example": {
                "model_key": "resnet50-原始",
                "image_data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
            }
        }


class PredictionResult(BaseModel):
    """预测结果模型"""
    
    predictions: Dict[str, float] = Field(..., description="预测结果，类别名称到概率的映射")
    inference_time: str = Field(..., description="推理时间信息")
    model_used: str = Field(..., description="使用的模型")
    top_prediction: str = Field(..., description="最高概率的预测类别")
    confidence: float = Field(..., ge=0, le=1, description="最高预测的置信度")
    
    class Config:
        schema_extra = {
            "example": {
                "predictions": {
                    "airplane": 0.8542,
                    "beach": 0.0876,
                    "bridge": 0.0234,
                    "forest": 0.0198,
                    "harbor": 0.0150
                },
                "inference_time": "推理时间: 23.45 ms",
                "model_used": "resnet50-原始",
                "top_prediction": "airplane",
                "confidence": 0.8542
            }
        }


class PredictionResponse(SuccessResponse):
    """预测响应模型"""
    
    result: PredictionResult = Field(..., description="预测结果")
    image_saved_path: Optional[str] = Field(default=None, description="保存的图像路径")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "预测完成",
                "result": {
                    "predictions": {
                        "airplane": 0.8542,
                        "beach": 0.0876,
                        "bridge": 0.0234,
                        "forest": 0.0198,
                        "harbor": 0.0150
                    },
                    "inference_time": "推理时间: 23.45 ms",
                    "model_used": "resnet50-原始",
                    "top_prediction": "airplane",
                    "confidence": 0.8542
                },
                "image_saved_path": "/imgs/12345678-1234-1234-1234-123456789abc.jpg",
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class UploadImageResponse(SuccessResponse):
    """图像上传响应模型"""
    
    image_path: str = Field(..., description="上传图像的保存路径")
    image_url: str = Field(..., description="图像访问URL")
    file_size: int = Field(..., description="文件大小(字节)")
    image_format: str = Field(..., description="图像格式")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "图像上传成功",
                "image_path": "/imgs/12345678-1234-1234-1234-123456789abc.jpg",
                "image_url": "/imgs/12345678-1234-1234-1234-123456789abc.jpg",
                "file_size": 245760,
                "image_format": "JPEG",
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class ModelLoadRequest(BaseModel):
    """模型加载请求模型"""
    
    model_key: str = Field(..., description="要加载的模型键名")
    force_reload: bool = Field(default=False, description="是否强制重新加载")
    
    @validator('model_key')
    def validate_model_key(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('模型键名不能为空')
        return v.strip()
    
    class Config:
        schema_extra = {
            "example": {
                "model_key": "resnet50-原始",
                "force_reload": False
            }
        }


class ModelLoadResponse(SuccessResponse):
    """模型加载响应模型"""
    
    model_key: str = Field(..., description="已加载的模型键名")
    load_time: float = Field(..., description="加载耗时(秒)")
    memory_usage: Optional[str] = Field(default=None, description="内存使用情况")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "模型加载成功",
                "model_key": "resnet50-原始",
                "load_time": 2.34,
                "memory_usage": "GPU: 1.2GB",
                "timestamp": "2023-12-01T12:00:00"
            }
        }
