# 使用 NVIDIA CUDA 官方镜像作为基础镜像
FROM nvidia/cuda:12.2.2-base-ubuntu22.04

# 安装 Python 和其他必要工具
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3-pip \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制 requirements.txt 文件并安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 复制项目文件
COPY src /app/src
COPY static /app/static
COPY outputs /app/outputs
COPY imgs /app/imgs
COPY old_dataset /app/old_dataset
COPY new_dataset /app/new_dataset

# 创建必要的目录
RUN mkdir -p /app/imgs /app/outputs/checkpoints /app/new_dataset

# 设置环境变量
ENV PYTHONPATH=/app
ENV CUDA_VISIBLE_DEVICES=0

# 暴露端口
EXPOSE 8000

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 设置容器启动时执行的命令 - 启动FastAPI应用
CMD ["uvicorn", "src.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]