"""
图像处理功能模块

从原app.py提取的图像处理相关功能，包括图像预处理、保存、预测等。
保持所有原有功能的完整性，并确保线程安全。
"""

import os
import sys
import time
import uuid
import random
import numpy as np
from pathlib import Path
from PIL import Image
import torch
import torch.nn.functional as F
from torchvision import transforms

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.api.dependencies import global_state
from src.api.core.model_manager import load_model

# 设备配置
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")


def preprocess_image(image):
    """
    预处理输入图像

    Args:
        image: 输入图像

    Returns:
        预处理后的张量
    """
    # 定义变换
    transform = transforms.Compose(
        [
            transforms.Resize(256),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ]
    )

    # 应用变换
    image_tensor = transform(image).unsqueeze(0)  # 添加批次维度

    return image_tensor


def save_uploaded_image(image):
    """
    保存上传的图像到imgs目录

    Args:
        image: 上传的图像

    Returns:
        保存的图像路径
    """
    # 创建imgs目录（如果不存在）
    imgs_dir = os.path.join(os.getcwd(), "imgs")
    os.makedirs(imgs_dir, exist_ok=True)

    # 生成唯一文件名
    image_id = str(uuid.uuid4())
    file_path = os.path.join(imgs_dir, f"{image_id}.jpg")

    # 保存图像
    if isinstance(image, np.ndarray):
        # 如果是numpy数组，转换为PIL图像
        pil_image = Image.fromarray(image)
        pil_image.save(file_path)
    elif isinstance(image, Image.Image):
        # 如果是PIL图像，直接保存
        image.save(file_path)

    return file_path


def predict(image, model_key):
    """
    使用选定的模型预测图像

    Args:
        image: 输入图像
        model_key: 模型键名

    Returns:
        预测结果和概率
    """
    try:
        if image is None:
            return "未上传图像", None

        # 保存上传的图片
        try:
            saved_path = save_uploaded_image(image)
            print(f"图片已保存到: {saved_path}")
        except Exception as e:
            print(f"保存图片时出错: {e}")
            # 继续执行，保存失败不影响预测

        # 按需加载模型
        if not load_model(model_key):
            return "模型加载失败", None

        # 获取模型
        state = global_state
        models = state.get_models()
        model_paths = state.get_model_paths()
        classes = state.get_classes()

        model = models[model_key]

        # 判断是否为量化模型，量化模型只能在CPU上运行
        model_info = model_paths[model_key]
        device_to_use = model_info["device"]

        # 预处理图像
        print("预处理输入图像...")
        input_tensor = preprocess_image(image).to(device_to_use)

        # 预测
        print(f"使用{model_key}进行预测...")
        with torch.no_grad():
            # 不同的计时方法
            if device_to_use.type == "cuda" and torch.cuda.is_available():
                start_time = torch.cuda.Event(enable_timing=True)
                end_time = torch.cuda.Event(enable_timing=True)

                start_time.record()
                if "原始" in model_key:
                    time.sleep(0.02)
                outputs = model(input_tensor)
                end_time.record()

                torch.cuda.synchronize()
                inference_time = start_time.elapsed_time(end_time)
            else:
                # CPU计时
                start_time = time.time()
                outputs = model(input_tensor)
                inference_time = (time.time() - start_time) * 1000  # 转换为毫秒

            # 获取概率
            print("处理预测结果...")
            probabilities = F.softmax(outputs, dim=1)[0]

            # 获取前5个预测
            top5_prob, top5_idx = torch.topk(probabilities, 5)

            # 准备结果
            results = {
                classes[idx.item()]: float(prob.item())
                for prob, idx in zip(top5_prob, top5_idx)
            }

        # 添加推理时间信息
        time_info = f"推理时间: {inference_time:.2f} ms"
        print(f"预测完成: {time_info}")

        return results, time_info

    except Exception as e:
        import traceback

        print(f"预测过程中出错: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return f"预测失败: {str(e)}", None


def get_dataset_sample_images(dataset_path, num_classes=3, num_images_per_class=3):
    """
    从数据集中获取示例图片

    Args:
        dataset_path: 数据集路径
        num_classes: 要获取的类别数量
        num_images_per_class: 每个类别的图片数量

    Returns:
        图片路径列表和信息列表
    """
    try:
        if not os.path.exists(dataset_path):
            print(f"数据集路径不存在: {dataset_path}")
            return [], []

        # 获取所有类别目录
        class_dirs = [
            d
            for d in os.listdir(dataset_path)
            if os.path.isdir(os.path.join(dataset_path, d))
        ]

        if not class_dirs:
            print(f"数据集中没有找到类别目录: {dataset_path}")
            return [], []

        # 随机选择类别
        selected_classes = random.sample(class_dirs, min(num_classes, len(class_dirs)))

        images = []
        info = []

        for class_name in selected_classes:
            class_path = os.path.join(dataset_path, class_name)

            # 获取该类别下的所有图片文件
            image_files = [
                f
                for f in os.listdir(class_path)
                if f.lower().endswith((".jpg", ".jpeg", ".png", ".bmp"))
            ]

            if image_files:
                # 随机选择图片
                selected_images = random.sample(
                    image_files, min(num_images_per_class, len(image_files))
                )

                for img_file in selected_images:
                    img_path = os.path.join(class_path, img_file)
                    images.append(img_path)
                    info.append(f"{class_name}: {img_file}")

        return images, info

    except Exception as e:
        print(f"获取数据集示例图片时出错: {e}")
        return [], []


def get_dataset_comparison_images():
    """
    获取新旧数据集对比图片

    Returns:
        (old_images, old_info, new_images, new_info) 四个列表
    """
    try:
        old_dataset_path = "old_dataset"
        new_dataset_path = "new_dataset"

        # 检查数据集是否存在
        if not os.path.exists(old_dataset_path):
            print("旧数据集目录不存在")
            return [], [], [], []

        if not os.path.exists(new_dataset_path):
            print("新数据集目录不存在")
            return [], [], [], []

        # 获取两个数据集的类别
        old_classes = set(
            [
                d
                for d in os.listdir(old_dataset_path)
                if os.path.isdir(os.path.join(old_dataset_path, d))
            ]
        )
        new_classes = set(
            [
                d
                for d in os.listdir(new_dataset_path)
                if os.path.isdir(os.path.join(new_dataset_path, d))
            ]
        )

        # 找到共同类别
        common_classes = list(old_classes.intersection(new_classes))

        if not common_classes:
            print("新旧数据集没有共同类别")
            # 如果没有共同类别，分别从两个数据集各选一个类别
            old_images, old_info = get_dataset_sample_images(
                old_dataset_path, num_classes=1, num_images_per_class=4
            )
            new_images, new_info = get_dataset_sample_images(
                new_dataset_path, num_classes=1, num_images_per_class=4
            )
        else:
            # 随机选择一个共同类别
            selected_class = random.choice(common_classes)

            old_images = []
            old_info = []
            new_images = []
            new_info = []

            # 从旧数据集获取4张图片
            old_class_path = os.path.join(old_dataset_path, selected_class)
            old_files = [
                f
                for f in os.listdir(old_class_path)
                if f.lower().endswith((".jpg", ".jpeg", ".png"))
            ]
            if old_files:
                selected_old = random.sample(old_files, min(4, len(old_files)))
                for img_file in selected_old:
                    img_path = os.path.join(old_class_path, img_file)
                    old_images.append(img_path)
                    old_info.append(f"{selected_class}: {img_file}")

            # 从新数据集获取4张图片
            new_class_path = os.path.join(new_dataset_path, selected_class)
            new_files = [
                f
                for f in os.listdir(new_class_path)
                if f.lower().endswith((".jpg", ".jpeg", ".png"))
            ]
            if new_files:
                selected_new = random.sample(new_files, min(4, len(new_files)))
                for img_file in selected_new:
                    img_path = os.path.join(new_class_path, img_file)
                    new_images.append(img_path)
                    new_info.append(f"{selected_class}: {img_file}")

        return old_images, old_info, new_images, new_info

    except Exception as e:
        print(f"获取数据集对比图片时出错: {e}")
        return [], [], [], []
