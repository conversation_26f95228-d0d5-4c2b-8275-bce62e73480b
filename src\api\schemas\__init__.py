"""
API数据模型模块

包含所有API请求和响应的Pydantic数据模型，用于数据验证和序列化。
确保与现有函数返回格式保持一致，便于前端处理。
"""

from .common import (
    BaseResponse,
    ErrorResponse,
    SuccessResponse,
    StatusResponse
)

from .prediction import (
    PredictionRequest,
    PredictionResponse,
    ModelInfo,
    ModelListResponse,
    UploadImageResponse
)

from .adaptive import (
    AdaptiveStatus,
    AdaptiveStatusResponse,
    DistributionCheck,
    DistributionCheckResponse,
    ThresholdUpdate,
    ThresholdUpdateResponse,
    ManualTuneResponse
)

from .comparison import (
    ComparisonResponse,
    ModelMetrics,
    PruningComparisonResponse,
    DistillationComparisonResponse,
    QuantizationComparisonResponse
)

__all__ = [
    # 通用响应模型
    'BaseResponse',
    'ErrorResponse', 
    'SuccessResponse',
    'StatusResponse',
    
    # 预测相关模型
    'PredictionRequest',
    'PredictionResponse',
    'ModelInfo',
    'ModelListResponse',
    'UploadImageResponse',
    
    # 自适应微调模型
    'AdaptiveStatus',
    'AdaptiveStatusResponse',
    'DistributionCheck',
    'DistributionCheckResponse',
    'ThresholdUpdate',
    'ThresholdUpdateResponse',
    'ManualTuneResponse',
    
    # 模型比较模型
    'ComparisonResponse',
    'ModelMetrics',
    'PruningComparisonResponse',
    'DistillationComparisonResponse',
    'QuantizationComparisonResponse'
]
