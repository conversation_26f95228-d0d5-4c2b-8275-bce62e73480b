"""
数据集管理数据模型

定义数据集相关API的请求和响应数据模型，包括数据集信息、图片对比等。
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, validator

from .common import SuccessResponse


class DatasetInfo(BaseModel):
    """数据集信息模型"""
    
    name: str = Field(..., description="数据集名称")
    path: str = Field(..., description="数据集路径")
    exists: bool = Field(..., description="数据集是否存在")
    total_classes: int = Field(default=0, ge=0, description="总类别数")
    total_images: int = Field(default=0, ge=0, description="总图片数")
    classes: List[str] = Field(default=[], description="类别列表")
    
    class Config:
        schema_extra = {
            "example": {
                "name": "old_dataset",
                "path": "/app/old_dataset",
                "exists": True,
                "total_classes": 21,
                "total_images": 2100,
                "classes": ["airplane", "beach", "bridge", "forest"]
            }
        }


class ImageInfo(BaseModel):
    """图片信息模型"""
    
    filename: str = Field(..., description="文件名")
    class_name: str = Field(..., description="类别名称")
    relative_path: str = Field(..., description="相对路径")
    size_bytes: Optional[int] = Field(default=None, ge=0, description="文件大小(字节)")
    
    class Config:
        schema_extra = {
            "example": {
                "filename": "airplane_001.jpg",
                "class_name": "airplane",
                "relative_path": "airplane/airplane_001.jpg",
                "size_bytes": 45678
            }
        }


class DatasetComparison(BaseModel):
    """数据集对比模型"""
    
    old_dataset: DatasetInfo = Field(..., description="旧数据集信息")
    new_dataset: DatasetInfo = Field(..., description="新数据集信息")
    old_images: List[ImageInfo] = Field(..., description="旧数据集示例图片")
    new_images: List[ImageInfo] = Field(..., description="新数据集示例图片")
    common_classes: List[str] = Field(..., description="共同类别")
    selected_class: Optional[str] = Field(default=None, description="选中的对比类别")
    
    class Config:
        schema_extra = {
            "example": {
                "old_dataset": {
                    "name": "old_dataset",
                    "path": "/app/old_dataset",
                    "exists": True,
                    "total_classes": 21,
                    "total_images": 2100,
                    "classes": ["airplane", "beach"]
                },
                "new_dataset": {
                    "name": "new_dataset", 
                    "path": "/app/new_dataset",
                    "exists": True,
                    "total_classes": 5,
                    "total_images": 50,
                    "classes": ["airplane", "beach"]
                },
                "old_images": [
                    {
                        "filename": "airplane_001.jpg",
                        "class_name": "airplane",
                        "relative_path": "airplane/airplane_001.jpg"
                    }
                ],
                "new_images": [
                    {
                        "filename": "airplane_new_001.jpg", 
                        "class_name": "airplane",
                        "relative_path": "airplane/airplane_new_001.jpg"
                    }
                ],
                "common_classes": ["airplane", "beach"],
                "selected_class": "airplane"
            }
        }


class DatasetComparisonResponse(SuccessResponse):
    """数据集对比响应模型"""
    
    comparison: DatasetComparison = Field(..., description="数据集对比结果")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "数据集对比获取成功",
                "comparison": {
                    "old_dataset": {"name": "old_dataset"},
                    "new_dataset": {"name": "new_dataset"},
                    "old_images": [],
                    "new_images": [],
                    "common_classes": [],
                    "selected_class": None
                },
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class DatasetInfoResponse(SuccessResponse):
    """数据集信息响应模型"""
    
    datasets: Dict[str, DatasetInfo] = Field(..., description="数据集信息字典")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "数据集信息获取成功",
                "datasets": {
                    "old_dataset": {
                        "name": "old_dataset",
                        "path": "/app/old_dataset",
                        "exists": True,
                        "total_classes": 21,
                        "total_images": 2100,
                        "classes": []
                    },
                    "new_dataset": {
                        "name": "new_dataset",
                        "path": "/app/new_dataset", 
                        "exists": False,
                        "total_classes": 0,
                        "total_images": 0,
                        "classes": []
                    }
                },
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class RefreshDatasetRequest(BaseModel):
    """刷新数据集请求模型"""
    
    force_refresh: bool = Field(default=False, description="是否强制刷新")
    
    class Config:
        schema_extra = {
            "example": {
                "force_refresh": True
            }
        }


class ImageAccessRequest(BaseModel):
    """图片访问请求模型"""
    
    image_path: str = Field(..., description="图片相对路径")
    thumbnail: bool = Field(default=False, description="是否返回缩略图")
    max_size: Optional[int] = Field(default=None, ge=50, le=2048, description="最大尺寸(像素)")
    
    @validator('image_path')
    def validate_image_path(cls, v):
        # 防止路径遍历攻击
        if '..' in v or v.startswith('/') or '\\' in v:
            raise ValueError('无效的图片路径')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "image_path": "airplane/airplane_001.jpg",
                "thumbnail": True,
                "max_size": 256
            }
        }


class DatasetStatistics(BaseModel):
    """数据集统计信息模型"""
    
    total_size_bytes: int = Field(..., ge=0, description="总大小(字节)")
    average_image_size: float = Field(..., ge=0, description="平均图片大小(字节)")
    class_distribution: Dict[str, int] = Field(..., description="类别分布")
    image_formats: Dict[str, int] = Field(..., description="图片格式分布")
    
    class Config:
        schema_extra = {
            "example": {
                "total_size_bytes": 1048576000,
                "average_image_size": 524288.0,
                "class_distribution": {
                    "airplane": 100,
                    "beach": 100,
                    "bridge": 100
                },
                "image_formats": {
                    "jpg": 250,
                    "png": 50
                }
            }
        }


class DatasetStatisticsResponse(SuccessResponse):
    """数据集统计响应模型"""
    
    statistics: Dict[str, DatasetStatistics] = Field(..., description="数据集统计信息")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "数据集统计获取成功",
                "statistics": {
                    "old_dataset": {
                        "total_size_bytes": 1048576000,
                        "average_image_size": 524288.0,
                        "class_distribution": {},
                        "image_formats": {}
                    }
                },
                "timestamp": "2023-12-01T12:00:00"
            }
        }
