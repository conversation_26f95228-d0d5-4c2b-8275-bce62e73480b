# 遥感图像分类项目部署指南

## 概述

本项目已从 Gradio 界面重构为 FastAPI 后端 + HTML/CSS/JS 前端的架构，支持 Docker 容器化部署。

## 架构说明

- **后端**: FastAPI 提供 RESTful API 服务
- **前端**: 原生 HTML/CSS/JS 实现用户界面
- **部署**: Docker 容器化部署，支持 GPU 加速

## 快速部署

### 1. 使用 Docker（推荐）

```bash
# 构建镜像
docker build -t yaogan-classification .

# 运行容器（GPU版本）
docker run -d \
  --name yaogan-app \
  --gpus all \
  -p 8000:8000 \
  -v $(pwd)/outputs:/app/outputs \
  -v $(pwd)/imgs:/app/imgs \
  -v $(pwd)/new_dataset:/app/new_dataset \
  -v $(pwd)/static:/app/static \
  yaogan-classification

# 运行容器（CPU版本）
docker run -d \
  --name yaogan-app \
  -p 8000:8000 \
  -v $(pwd)/outputs:/app/outputs \
  -v $(pwd)/imgs:/app/imgs \
  -v $(pwd)/new_dataset:/app/new_dataset \
  -v $(pwd)/static:/app/static \
  yaogan-classification
```

### 2. 直接运行

```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用
uvicorn src.api.main:app --host 0.0.0.0 --port 8000
```

## 服务访问

- **主页面**: http://localhost:8000/static/index.html
- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **API 信息**: http://localhost:8000/api/info

## 配置说明

### 环境变量

- `PYTHONPATH`: Python 模块路径，默认为 `/app`
- `CUDA_VISIBLE_DEVICES`: GPU 设备 ID，默认为 `0`
- `FASTAPI_ENV`: 运行环境，可设置为 `production`
- `MODEL_BASE_PATH`: 模型文件基础路径
- `DATASET_BASE_PATH`: 数据集基础路径

### 端口配置

- **FastAPI 应用**: 8000 端口

### 数据卷挂载

- `./outputs:/app/outputs` - 模型输出和检查点
- `./imgs:/app/imgs` - 上传的图片文件
- `./new_dataset:/app/new_dataset` - 新数据集
- `./static:/app/static` - 静态文件

## GPU 支持

项目支持 NVIDIA GPU 加速：

1. **Docker 方式**: 使用 `--gpus all` 参数
2. **环境检查**: 启动时会自动检测 CUDA 环境

## 健康检查

系统提供健康检查：

1. **Docker 健康检查**: 每 30 秒检查一次
2. **应用健康检查**: `/health` 端点提供详细状态

## 日志管理

- **应用日志**: 通过 uvicorn 输出到标准输出
- **Docker 日志**: 使用 `docker logs yaogan-app` 查看

## 故障排除

### 常见问题

1. **端口冲突**: 检查 8000 端口是否被占用
2. **GPU 不可用**: 确认 NVIDIA Docker 支持已安装
3. **静态文件 404**: 检查 static 目录是否正确挂载
4. **API 调用失败**: 查看应用日志排查错误

### 调试命令

```bash
# 检查容器状态
docker ps

# 查看应用日志
docker logs yaogan-app

# 进入容器调试
docker exec -it yaogan-app bash

# 检查健康状态
curl http://localhost:8000/health

# 测试API
curl http://localhost:8000/api/info
```

## 生产环境优化

### 性能优化

1. **增加 Worker 数量**: 修改 uvicorn 的 `--workers` 参数
2. **数据库连接池**: 如需要可配置数据库连接池

### 安全配置

1. **CORS 限制**: 生产环境应限制允许的域名
2. **HTTPS 配置**: 配置 SSL 证书启用 HTTPS
3. **防火墙设置**: 限制不必要的端口访问
4. **访问日志**: 启用详细的访问日志记录

### 监控配置

1. **健康检查**: 配置外部监控系统
2. **性能监控**: 可集成 Prometheus 等监控工具
3. **日志收集**: 可配置 ELK 等日志收集系统
4. **告警设置**: 配置关键指标告警

## 更新部署

```bash
# 停止并删除容器
docker stop yaogan-app
docker rm yaogan-app

# 拉取最新代码
git pull

# 重新构建镜像
docker build -t yaogan-classification .

# 启动新容器
docker run -d \
  --name yaogan-app \
  --gpus all \
  -p 8000:8000 \
  -v $(pwd)/outputs:/app/outputs \
  -v $(pwd)/imgs:/app/imgs \
  -v $(pwd)/new_dataset:/app/new_dataset \
  -v $(pwd)/static:/app/static \
  yaogan-classification

# 验证部署
curl http://localhost:8000/health
```

## 备份和恢复

### 备份

```bash
# 备份模型和输出
tar -czf backup-$(date +%Y%m%d).tar.gz outputs/

# 备份配置文件
cp Dockerfile Dockerfile.bak
```

### 恢复

```bash
# 恢复模型和输出
tar -xzf backup-YYYYMMDD.tar.gz

# 重启服务
docker restart yaogan-app
```
