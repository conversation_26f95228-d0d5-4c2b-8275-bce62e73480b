/**
 * API调用封装模块
 * 统一处理HTTP请求，包括错误处理、加载状态、重试机制等
 */

class APIClient {
    constructor(baseURL = '') {
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
        };
        this.requestInterceptors = [];
        this.responseInterceptors = [];
    }

    /**
     * 添加请求拦截器
     */
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
    }

    /**
     * 添加响应拦截器
     */
    addResponseInterceptor(interceptor) {
        this.responseInterceptors.push(interceptor);
    }

    /**
     * 执行请求拦截器
     */
    async executeRequestInterceptors(config) {
        let result = config;
        for (const interceptor of this.requestInterceptors) {
            result = await interceptor(result);
        }
        return result;
    }

    /**
     * 执行响应拦截器
     */
    async executeResponseInterceptors(response) {
        let result = response;
        for (const interceptor of this.responseInterceptors) {
            result = await interceptor(result);
        }
        return result;
    }

    /**
     * 通用请求方法
     */
    async request(url, options = {}) {
        try {
            // 构建完整URL
            const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`;
            
            // 默认配置
            const config = {
                method: 'GET',
                headers: { ...this.defaultHeaders },
                ...options
            };

            // 执行请求拦截器
            const interceptedConfig = await this.executeRequestInterceptors(config);

            // 发送请求
            const response = await fetch(fullURL, interceptedConfig);

            // 执行响应拦截器
            const interceptedResponse = await this.executeResponseInterceptors(response);

            // 检查响应状态
            if (!interceptedResponse.ok) {
                throw new Error(`HTTP ${interceptedResponse.status}: ${interceptedResponse.statusText}`);
            }

            // 解析响应数据
            const contentType = interceptedResponse.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await interceptedResponse.json();
            } else {
                return await interceptedResponse.text();
            }

        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    /**
     * GET请求
     */
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullURL = queryString ? `${url}?${queryString}` : url;
        return this.request(fullURL, { method: 'GET' });
    }

    /**
     * POST请求
     */
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT请求
     */
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     */
    async delete(url) {
        return this.request(url, { method: 'DELETE' });
    }

    /**
     * 文件上传
     */
    async upload(url, formData) {
        return this.request(url, {
            method: 'POST',
            headers: {}, // 让浏览器自动设置Content-Type
            body: formData
        });
    }

    /**
     * 下载文件
     */
    async download(url, filename) {
        try {
            const response = await fetch(`${this.baseURL}${url}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const blob = await response.blob();
            const downloadURL = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadURL;
            link.download = filename || 'download';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(downloadURL);

        } catch (error) {
            console.error('文件下载失败:', error);
            throw error;
        }
    }
}

// 创建全局API客户端实例
const apiClient = new APIClient();

// 添加请求拦截器 - 显示加载状态
apiClient.addRequestInterceptor(async (config) => {
    if (window.showLoading) {
        window.showLoading();
    }
    return config;
});

// 添加响应拦截器 - 隐藏加载状态
apiClient.addResponseInterceptor(async (response) => {
    if (window.hideLoading) {
        window.hideLoading();
    }
    return response;
});

// API端点定义
const API_ENDPOINTS = {
    // 系统信息
    HEALTH: '/health',
    INFO: '/api/info',
    
    // 预测相关
    PREDICTION_PREDICT: '/api/prediction/predict',
    PREDICTION_MODELS: '/api/prediction/models',
    
    // 自适应微调
    ADAPTIVE_START: '/api/adaptive/start-monitoring',
    ADAPTIVE_STOP: '/api/adaptive/stop-monitoring',
    ADAPTIVE_STATUS: '/api/adaptive/status',
    ADAPTIVE_MANUAL: '/api/adaptive/manual-tune',
    ADAPTIVE_DISTRIBUTION: '/api/adaptive/check-distribution',
    ADAPTIVE_THRESHOLD: '/api/adaptive/update-threshold',
    
    // 模型比较
    COMPARISON_OVERVIEW: '/api/comparison/overview',
    COMPARISON_PRUNING: '/api/comparison/pruning',
    COMPARISON_DISTILLATION: '/api/comparison/distillation',
    COMPARISON_QUANTIZATION: '/api/comparison/quantization',
    
    // 数据集管理
    DATASET_INFO: '/api/dataset/info',
    DATASET_COMPARISON: '/api/dataset/comparison',
    DATASET_REFRESH: '/api/dataset/refresh',
    DATASET_IMAGES: '/api/dataset/images'
};

// 具体API调用函数
const API = {
    // 系统信息
    async getHealth() {
        return apiClient.get(API_ENDPOINTS.HEALTH);
    },

    async getInfo() {
        return apiClient.get(API_ENDPOINTS.INFO);
    },

    // 预测功能
    async predict(imageFile, modelName) {
        const formData = new FormData();
        formData.append('image', imageFile);
        formData.append('model_name', modelName);
        return apiClient.upload(API_ENDPOINTS.PREDICTION_PREDICT, formData);
    },

    async getAvailableModels() {
        return apiClient.get(API_ENDPOINTS.PREDICTION_MODELS);
    },

    // 自适应微调
    async startAdaptiveMonitoring() {
        return apiClient.post(API_ENDPOINTS.ADAPTIVE_START);
    },

    async stopAdaptiveMonitoring() {
        return apiClient.post(API_ENDPOINTS.ADAPTIVE_STOP);
    },

    async getAdaptiveStatus() {
        return apiClient.get(API_ENDPOINTS.ADAPTIVE_STATUS);
    },

    async manualFineTune() {
        return apiClient.post(API_ENDPOINTS.ADAPTIVE_MANUAL);
    },

    async checkDataDistribution() {
        return apiClient.get(API_ENDPOINTS.ADAPTIVE_DISTRIBUTION);
    },

    async updateDistributionThreshold(threshold) {
        return apiClient.post(API_ENDPOINTS.ADAPTIVE_THRESHOLD, { threshold });
    },

    // 模型比较
    async getComparisonOverview() {
        return apiClient.get(API_ENDPOINTS.COMPARISON_OVERVIEW);
    },

    async getPruningComparison(modelType, format = 'html') {
        return apiClient.get(`${API_ENDPOINTS.COMPARISON_PRUNING}/${modelType}`, { format });
    },

    async getDistillationComparison(modelType, format = 'html') {
        return apiClient.get(`${API_ENDPOINTS.COMPARISON_DISTILLATION}/${modelType}`, { format });
    },

    async getQuantizationComparison(modelType, format = 'html') {
        return apiClient.get(`${API_ENDPOINTS.COMPARISON_QUANTIZATION}/${modelType}`, { format });
    },

    // 数据集管理
    async getDatasetInfo() {
        return apiClient.get(API_ENDPOINTS.DATASET_INFO);
    },

    async getDatasetComparison() {
        return apiClient.get(API_ENDPOINTS.DATASET_COMPARISON);
    },

    async refreshDatasetComparison(forceRefresh = false) {
        return apiClient.post(API_ENDPOINTS.DATASET_REFRESH, { force_refresh: forceRefresh });
    },

    async getDatasetImage(imagePath, thumbnail = false, maxSize = null) {
        const params = { thumbnail };
        if (maxSize) params.max_size = maxSize;
        const queryString = new URLSearchParams(params).toString();
        return `${API_ENDPOINTS.DATASET_IMAGES}/${imagePath}?${queryString}`;
    }
};

// 错误处理工具
const ErrorHandler = {
    /**
     * 处理API错误
     */
    handleAPIError(error, context = '') {
        console.error(`API错误 ${context}:`, error);
        
        let message = '操作失败，请稍后重试';
        
        if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
            message = '网络连接失败，请检查网络连接';
        } else if (error.message.includes('HTTP 404')) {
            message = '请求的资源不存在';
        } else if (error.message.includes('HTTP 500')) {
            message = '服务器内部错误，请联系管理员';
        } else if (error.message.includes('HTTP 400')) {
            message = '请求参数错误，请检查输入';
        }
        
        if (window.showError) {
            window.showError(message);
        }
        
        return message;
    },

    /**
     * 重试机制
     */
    async retry(fn, maxRetries = 3, delay = 1000) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await fn();
            } catch (error) {
                if (i === maxRetries - 1) {
                    throw error;
                }
                await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
            }
        }
    }
};

// 导出API对象
window.API = API;
window.ErrorHandler = ErrorHandler;
