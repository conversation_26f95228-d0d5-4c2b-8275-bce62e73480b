"""
数据集管理API路由

提供数据集相关的API端点，包括数据集图片对比展示、数据集信息查询等功能。
支持图片文件的访问和展示，确保安全性防止路径遍历攻击。
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, Query, Path as PathParam, Response
from fastapi.responses import FileResponse, JSONResponse
from PIL import Image
import logging
from functools import lru_cache
import mimetypes

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.api.dependencies import get_global_state, handle_api_error
from src.api.schemas.dataset import (
    DatasetComparisonResponse,
    DatasetInfoResponse,
    DatasetStatisticsResponse,
    RefreshDatasetRequest,
    DatasetInfo,
    DatasetComparison,
    ImageInfo,
    DatasetStatistics,
)
from src.api.core.image_processor import get_dataset_comparison_images

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/api/dataset",
    tags=["数据集管理"],
    responses={404: {"description": "Not found"}},
)

# 支持的图片格式
SUPPORTED_IMAGE_FORMATS = {".jpg", ".jpeg", ".png", ".bmp", ".gif", ".tiff"}

# 数据集基础路径
DATASET_BASE_PATH = project_root


def _get_dataset_info(dataset_name: str, dataset_path: Path) -> DatasetInfo:
    """
    获取数据集信息

    Args:
        dataset_name: 数据集名称
        dataset_path: 数据集路径

    Returns:
        数据集信息
    """
    try:
        if not dataset_path.exists():
            return DatasetInfo(
                name=dataset_name,
                path=str(dataset_path),
                exists=False,
                total_classes=0,
                total_images=0,
                classes=[],
            )

        # 获取所有类别目录
        classes = []
        total_images = 0

        for item in dataset_path.iterdir():
            if item.is_dir():
                class_name = item.name
                classes.append(class_name)

                # 统计该类别下的图片数量
                image_count = 0
                for img_file in item.iterdir():
                    if img_file.suffix.lower() in SUPPORTED_IMAGE_FORMATS:
                        image_count += 1

                total_images += image_count

        return DatasetInfo(
            name=dataset_name,
            path=str(dataset_path),
            exists=True,
            total_classes=len(classes),
            total_images=total_images,
            classes=sorted(classes),
        )

    except Exception as e:
        logger.error(f"获取数据集信息失败: {e}")
        return DatasetInfo(
            name=dataset_name,
            path=str(dataset_path),
            exists=False,
            total_classes=0,
            total_images=0,
            classes=[],
        )


def _validate_image_path(image_path: str) -> Path:
    """
    验证图片路径，防止路径遍历攻击

    Args:
        image_path: 图片相对路径

    Returns:
        验证后的完整路径

    Raises:
        HTTPException: 路径无效时抛出异常
    """
    # 防止路径遍历攻击
    if ".." in image_path or image_path.startswith("/") or "\\" in image_path:
        raise HTTPException(status_code=400, detail="无效的图片路径")

    # 构建完整路径
    full_path = DATASET_BASE_PATH / image_path

    # 确保路径在项目根目录下
    try:
        full_path.resolve().relative_to(DATASET_BASE_PATH.resolve())
    except ValueError:
        raise HTTPException(status_code=400, detail="图片路径超出允许范围")

    # 检查文件是否存在
    if not full_path.exists():
        raise HTTPException(status_code=404, detail="图片文件不存在")

    # 检查是否为支持的图片格式
    if full_path.suffix.lower() not in SUPPORTED_IMAGE_FORMATS:
        raise HTTPException(status_code=400, detail="不支持的图片格式")

    return full_path


@router.get("/info", response_model=DatasetInfoResponse)
async def get_dataset_info():
    """
    获取数据集基本信息

    Returns:
        数据集信息
    """
    try:
        datasets = {}

        # 获取旧数据集信息
        old_dataset_path = DATASET_BASE_PATH / "old_dataset"
        datasets["old_dataset"] = _get_dataset_info("old_dataset", old_dataset_path)

        # 获取新数据集信息
        new_dataset_path = DATASET_BASE_PATH / "new_dataset"
        datasets["new_dataset"] = _get_dataset_info("new_dataset", new_dataset_path)

        return DatasetInfoResponse(
            success=True, message="数据集信息获取成功", datasets=datasets
        )

    except Exception as e:
        logger.error(f"获取数据集信息失败: {str(e)}")
        raise handle_api_error(e)


@router.get("/comparison", response_model=DatasetComparisonResponse)
async def get_dataset_comparison():
    """
    获取新旧数据集对比图片

    Returns:
        数据集对比结果
    """
    try:
        # 获取数据集信息
        old_dataset_path = DATASET_BASE_PATH / "old_dataset"
        new_dataset_path = DATASET_BASE_PATH / "new_dataset"

        old_dataset_info = _get_dataset_info("old_dataset", old_dataset_path)
        new_dataset_info = _get_dataset_info("new_dataset", new_dataset_path)

        # 获取对比图片
        old_images_paths, old_info, new_images_paths, new_info = (
            get_dataset_comparison_images()
        )

        # 转换为ImageInfo对象
        old_images = []
        for i, (img_path, info) in enumerate(zip(old_images_paths, old_info)):
            try:
                rel_path = Path(img_path).relative_to(DATASET_BASE_PATH)
                class_name = info.split(":")[0].strip()
                filename = Path(img_path).name

                # 获取文件大小
                size_bytes = None
                if os.path.exists(img_path):
                    size_bytes = os.path.getsize(img_path)

                old_images.append(
                    ImageInfo(
                        filename=filename,
                        class_name=class_name,
                        relative_path=str(rel_path),
                        size_bytes=size_bytes,
                    )
                )
            except Exception as e:
                logger.warning(f"处理旧数据集图片信息失败: {e}")

        new_images = []
        for i, (img_path, info) in enumerate(zip(new_images_paths, new_info)):
            try:
                rel_path = Path(img_path).relative_to(DATASET_BASE_PATH)
                class_name = info.split(":")[0].strip()
                filename = Path(img_path).name

                # 获取文件大小
                size_bytes = None
                if os.path.exists(img_path):
                    size_bytes = os.path.getsize(img_path)

                new_images.append(
                    ImageInfo(
                        filename=filename,
                        class_name=class_name,
                        relative_path=str(rel_path),
                        size_bytes=size_bytes,
                    )
                )
            except Exception as e:
                logger.warning(f"处理新数据集图片信息失败: {e}")

        # 计算共同类别
        old_classes = set(old_dataset_info.classes)
        new_classes = set(new_dataset_info.classes)
        common_classes = list(old_classes.intersection(new_classes))

        # 确定选中的类别
        selected_class = None
        if old_images and new_images:
            # 如果有对比图片，尝试找到共同类别
            old_class = old_images[0].class_name
            new_class = new_images[0].class_name
            if old_class == new_class:
                selected_class = old_class

        comparison = DatasetComparison(
            old_dataset=old_dataset_info,
            new_dataset=new_dataset_info,
            old_images=old_images,
            new_images=new_images,
            common_classes=sorted(common_classes),
            selected_class=selected_class,
        )

        return DatasetComparisonResponse(
            success=True, message="数据集对比获取成功", comparison=comparison
        )

    except Exception as e:
        logger.error(f"获取数据集对比失败: {str(e)}")
        raise handle_api_error(e)


@router.post("/refresh", response_model=DatasetComparisonResponse)
async def refresh_dataset_comparison(request: RefreshDatasetRequest):
    """
    刷新数据集对比图片

    Args:
        request: 刷新请求

    Returns:
        新的数据集对比结果
    """
    try:
        # 清除缓存（如果有的话）
        if hasattr(get_dataset_comparison_images, "cache_clear"):
            get_dataset_comparison_images.cache_clear()

        # 重新获取对比数据
        return await get_dataset_comparison()

    except Exception as e:
        logger.error(f"刷新数据集对比失败: {str(e)}")
        raise handle_api_error(e)


@router.get("/images/{image_path:path}")
async def get_image(
    image_path: str = PathParam(..., description="图片相对路径"),
    thumbnail: bool = Query(default=False, description="是否返回缩略图"),
    max_size: Optional[int] = Query(
        default=None, ge=50, le=2048, description="最大尺寸(像素)"
    ),
):
    """
    获取图片文件

    Args:
        image_path: 图片相对路径
        thumbnail: 是否返回缩略图
        max_size: 最大尺寸(像素)

    Returns:
        图片文件
    """
    try:
        # 验证路径
        full_path = _validate_image_path(image_path)

        # 如果不需要缩略图，直接返回原图
        if not thumbnail and max_size is None:
            # 获取MIME类型
            mime_type, _ = mimetypes.guess_type(str(full_path))
            if mime_type is None:
                mime_type = "image/jpeg"

            return FileResponse(
                path=str(full_path), media_type=mime_type, filename=full_path.name
            )

        # 生成缩略图
        try:
            with Image.open(full_path) as img:
                # 确定目标尺寸
                target_size = max_size or 256

                # 计算缩放比例
                width, height = img.size
                if width > height:
                    new_width = target_size
                    new_height = int(height * target_size / width)
                else:
                    new_height = target_size
                    new_width = int(width * target_size / height)

                # 缩放图片
                img_resized = img.resize(
                    (new_width, new_height), Image.Resampling.LANCZOS
                )

                # 转换为RGB（如果需要）
                if img_resized.mode != "RGB":
                    img_resized = img_resized.convert("RGB")

                # 保存到临时文件
                import tempfile
                import io

                img_bytes = io.BytesIO()
                img_resized.save(img_bytes, format="JPEG", quality=85)
                img_bytes.seek(0)

                return Response(
                    content=img_bytes.getvalue(),
                    media_type="image/jpeg",
                    headers={
                        "Content-Disposition": f"inline; filename=thumb_{full_path.name}",
                        "Cache-Control": "public, max-age=3600",
                    },
                )

        except Exception as e:
            logger.error(f"生成缩略图失败: {e}")
            # 如果缩略图生成失败，返回原图
            return FileResponse(path=str(full_path))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取图片失败: {str(e)}")
        raise handle_api_error(e)
