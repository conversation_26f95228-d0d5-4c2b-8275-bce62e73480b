"""
FastAPI主应用文件

遥感图像分类项目的FastAPI后端服务主入口。
提供RESTful API接口，支持模型预测、自适应微调、模型比较等功能。
"""

import os
import sys
from pathlib import Path
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import uvicorn
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.api.dependencies import get_global_state, handle_api_error
from src.api.routers import prediction, adaptive, comparison, dataset

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用实例
app = FastAPI(
    title="遥感图像分类API",
    description="基于深度学习的遥感图像场景分类系统API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置静态文件服务
static_dir = project_root / "static"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    logger.info(f"静态文件服务已配置: {static_dir}")
else:
    logger.warning(f"静态文件目录不存在: {static_dir}")

# 配置图片文件服务
imgs_dir = project_root / "imgs"
if imgs_dir.exists():
    app.mount("/imgs", StaticFiles(directory=str(imgs_dir)), name="imgs")
    logger.info(f"图片文件服务已配置: {imgs_dir}")
else:
    logger.warning(f"图片目录不存在: {imgs_dir}")

# 注册路由
app.include_router(prediction.router)
app.include_router(adaptive.router)
app.include_router(comparison.router)
app.include_router(dataset.router)


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"全局异常: {str(exc)}")
    http_exc = handle_api_error(exc)
    return JSONResponse(
        status_code=http_exc.status_code, content={"detail": http_exc.detail}
    )


@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "遥感图像分类API服务",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc",
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查全局状态
        state = get_global_state()

        # 检查关键目录
        project_root_exists = project_root.exists()
        static_exists = static_dir.exists()
        imgs_exists = imgs_dir.exists()

        return {
            "status": "healthy",
            "timestamp": str(Path(__file__).stat().st_mtime),
            "project_root": str(project_root),
            "directories": {
                "project_root": project_root_exists,
                "static": static_exists,
                "imgs": imgs_exists,
            },
            "global_state": {
                "models_count": len(state.get_models()),
                "model_paths_count": len(state.get_model_paths()),
                "classes_count": len(state.get_classes()),
                "current_run_dir": state.get_current_run_dir(),
                "adaptive_monitoring": state.get_adaptive_monitoring(),
            },
        }
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@app.get("/api/info")
async def api_info():
    """API信息端点"""
    return {
        "name": "遥感图像分类API",
        "version": "1.0.0",
        "description": "基于深度学习的遥感图像场景分类系统",
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "redoc": "/redoc",
            "static_files": "/static",
            "image_files": "/imgs",
        },
        "features": ["模型预测", "自适应微调", "模型比较", "数据集管理"],
    }


# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    logger.info("FastAPI应用启动中...")

    # 创建必要的目录
    os.makedirs(imgs_dir, exist_ok=True)

    # 初始化全局状态
    state = get_global_state()
    logger.info("全局状态已初始化")

    logger.info("FastAPI应用启动完成")


# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理操作"""
    logger.info("FastAPI应用关闭中...")

    # 清理资源
    state = get_global_state()
    state.set_adaptive_monitoring(False)

    logger.info("FastAPI应用已关闭")


if __name__ == "__main__":
    # 开发环境启动配置
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True, log_level="info")
