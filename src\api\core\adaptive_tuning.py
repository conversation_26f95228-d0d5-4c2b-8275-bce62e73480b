"""
自适应微调功能模块

从原app.py提取的自适应微调相关功能，包括微调、蒸馏、分布检查等。
保持所有原有功能的完整性，并确保线程安全。
"""

import os
import sys
import time
import shutil
import datetime
import threading
import numpy as np
from pathlib import Path
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入项目模块
from src.data.dataset import OldDataset, create_dataloaders, get_transforms
from src.optimization.distillation import create_student_model
from src.api.dependencies import global_state, global_lock
from src.api.core.model_manager import load_model, discover_available_models

# 设备配置
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 自适应微调相关全局变量
LAST_CHECK_TIME = None
DISTRIBUTION_THRESHOLD = 0.1
OLD_FEATURES = None
NEW_FEATURES = None


def extract_features_from_dataset(
    dataset_path, model_key="resnet50-原始", num_samples=1000
):
    """
    从数据集中提取特征用于分布比较

    Args:
        dataset_path: 数据集路径
        model_key: 用于特征提取的模型
        num_samples: 采样数量

    Returns:
        特征向量和标签
    """
    try:
        # 加载模型
        if not load_model(model_key):
            print(f"无法加载模型 {model_key} 进行特征提取")
            return None, None

        state = global_state
        models = state.get_models()
        model = models[model_key]
        model.eval()

        # 创建数据集
        dataset = OldDataset(root_dir=dataset_path, transform=get_transforms("val"))

        # 限制采样数量
        if len(dataset) > num_samples:
            indices = np.random.choice(len(dataset), num_samples, replace=False)
            subset_dataset = torch.utils.data.Subset(dataset, indices)
        else:
            subset_dataset = dataset

        dataloader = DataLoader(subset_dataset, batch_size=32, shuffle=False)

        features = []
        labels = []

        with torch.no_grad():
            for batch_images, batch_labels in dataloader:
                batch_images = batch_images.to(DEVICE)

                # 提取特征（倒数第二层）
                if hasattr(model, "classifier"):
                    # ResNet等模型
                    x = model.conv1(batch_images)
                    x = model.bn1(x)
                    x = model.relu(x)
                    x = model.maxpool(x)
                    x = model.layer1(x)
                    x = model.layer2(x)
                    x = model.layer3(x)
                    x = model.layer4(x)
                    x = model.avgpool(x)
                    x = torch.flatten(x, 1)
                else:
                    # 其他模型，使用输出作为特征
                    x = model(batch_images)

                features.append(x.cpu().numpy())
                labels.append(batch_labels.numpy())

        features = np.concatenate(features, axis=0)
        labels = np.concatenate(labels, axis=0)

        return features, labels

    except Exception as e:
        print(f"特征提取过程中出错: {e}")
        import traceback

        traceback.print_exc()
        return None, None


def compute_distribution_difference(old_features, new_features):
    """
    计算两个特征分布之间的差异

    Args:
        old_features: 旧数据特征
        new_features: 新数据特征

    Returns:
        分布差异值（0-1之间，越大表示差异越大）
    """
    try:
        # 方法1: KL散度（通过直方图近似）
        old_mean = np.mean(old_features, axis=0)
        new_mean = np.mean(new_features, axis=0)

        # 计算均值的余弦相似度
        cos_sim = np.dot(old_mean, new_mean) / (
            np.linalg.norm(old_mean) * np.linalg.norm(new_mean)
        )
        mean_diff = 1 - cos_sim

        # 方法2: 方差差异
        old_var = np.var(old_features, axis=0)
        new_var = np.var(new_features, axis=0)
        var_diff = np.mean(np.abs(old_var - new_var) / (old_var + 1e-8))

        # 方法3: MMD近似（使用核的期望）
        old_norm = np.linalg.norm(old_features, axis=1, keepdims=True)
        new_norm = np.linalg.norm(new_features, axis=1, keepdims=True)

        old_normalized = old_features / (old_norm + 1e-8)
        new_normalized = new_features / (new_norm + 1e-8)

        old_center = np.mean(old_normalized, axis=0)
        new_center = np.mean(new_normalized, axis=0)
        mmd_approx = np.linalg.norm(old_center - new_center)

        # 综合分数
        total_diff = 0.4 * mean_diff + 0.3 * var_diff + 0.3 * mmd_approx

        print(
            f"分布差异分析: 均值差异={mean_diff:.4f}, 方差差异={var_diff:.4f}, MMD近似={mmd_approx:.4f}"
        )
        print(f"综合差异分数: {total_diff:.4f}")

        return float(total_diff)

    except Exception as e:
        print(f"计算分布差异时出错: {e}")
        return 0.0


def check_data_distribution():
    """
    检查数据分布变化

    Returns:
        是否需要微调的布尔值和差异分数
    """
    global OLD_FEATURES, NEW_FEATURES

    try:
        print("开始检查数据分布变化...")

        # 检查新数据目录是否存在
        new_dataset_path = "new_dataset"
        if not os.path.exists(new_dataset_path):
            print("新数据目录不存在，跳过检查")
            return False, 0.0

        # 检查新数据目录是否有数据
        has_new_data = False
        for class_dir in os.listdir(new_dataset_path):
            class_path = os.path.join(new_dataset_path, class_dir)
            if os.path.isdir(class_path) and len(os.listdir(class_path)) > 0:
                has_new_data = True
                break

        if not has_new_data:
            print("新数据目录为空，跳过检查")
            return False, 0.0

        # 提取旧数据特征（如果还没有）
        if OLD_FEATURES is None:
            print("提取旧数据特征...")
            OLD_FEATURES, _ = extract_features_from_dataset("old_dataset")
            if OLD_FEATURES is None:
                print("无法提取旧数据特征")
                return False, 0.0

        # 提取新数据特征
        print("提取新数据特征...")
        NEW_FEATURES, _ = extract_features_from_dataset(new_dataset_path)
        if NEW_FEATURES is None:
            print("无法提取新数据特征")
            return False, 0.0

        # 计算分布差异
        diff_score = compute_distribution_difference(OLD_FEATURES, NEW_FEATURES)

        # 判断是否需要微调
        needs_fine_tuning = diff_score > DISTRIBUTION_THRESHOLD

        print(f"分布差异分数: {diff_score:.4f}, 阈值: {DISTRIBUTION_THRESHOLD}")
        print(f"是否需要微调: {needs_fine_tuning}")

        return needs_fine_tuning, diff_score

    except Exception as e:
        print(f"检查数据分布时出错: {e}")
        import traceback

        traceback.print_exc()
        return False, 0.0


def fine_tune_model(model_type="resnet50", epochs=10, learning_rate=1e-5):
    """
    微调指定模型

    Args:
        model_type: 模型类型
        epochs: 微调轮数
        learning_rate: 学习率

    Returns:
        是否微调成功
    """
    state = global_state
    fine_tuning_status = state.get_fine_tuning_status()

    try:
        fine_tuning_status["running"] = True
        fine_tuning_status["message"] = "开始微调..."
        state.set_fine_tuning_status(fine_tuning_status)

        print(f"开始微调模型: {model_type}")

        # 检查模型是否存在
        model_key = f"{model_type}-原始"
        model_paths = state.get_model_paths()
        if model_key not in model_paths:
            fine_tuning_status["message"] = f"未找到模型 {model_key}"
            fine_tuning_status["running"] = False
            state.set_fine_tuning_status(fine_tuning_status)
            return False

        # 合并新旧数据
        print("合并新旧数据...")
        combined_data_path = "combined_dataset"
        if os.path.exists(combined_data_path):
            shutil.rmtree(combined_data_path)

        # 复制旧数据
        shutil.copytree("old_dataset", combined_data_path)

        # 复制新数据
        new_dataset_path = "new_dataset"
        if os.path.exists(new_dataset_path):
            for class_dir in os.listdir(new_dataset_path):
                old_class_path = os.path.join(combined_data_path, class_dir)
                new_class_path = os.path.join(new_dataset_path, class_dir)

                if os.path.isdir(new_class_path):
                    # 如果类别目录不存在，创建它
                    os.makedirs(old_class_path, exist_ok=True)

                    # 复制新图片到对应类别目录
                    for img_file in os.listdir(new_class_path):
                        if img_file.endswith((".jpg", ".jpeg", ".png")):
                            src = os.path.join(new_class_path, img_file)
                            dst = os.path.join(old_class_path, f"new_{img_file}")
                            shutil.copy2(src, dst)

        fine_tuning_status["message"] = "创建数据加载器..."
        state.set_fine_tuning_status(fine_tuning_status)

        # 创建数据加载器
        train_loader, val_loader, test_loader, classes = create_dataloaders(
            data_dir=combined_data_path, batch_size=64, train_ratio=0.8, val_ratio=0.1
        )

        print(
            f"合并后数据集大小: 训练集={len(train_loader.dataset)}, 验证集={len(val_loader.dataset)}"
        )

        # 加载原始模型
        fine_tuning_status["message"] = "加载原始模型..."
        state.set_fine_tuning_status(fine_tuning_status)
        if not load_model(model_key):
            fine_tuning_status["message"] = "加载原始模型失败"
            fine_tuning_status["running"] = False
            state.set_fine_tuning_status(fine_tuning_status)
            return False

        models = state.get_models()
        model = models[model_key]
        model.train()

        # 设置优化器
        optimizer = optim.Adam(model.parameters(), lr=learning_rate)
        criterion = nn.CrossEntropyLoss()

        # 微调训练
        print(f"开始微调训练，共{epochs}轮...")
        for epoch in range(epochs):
            fine_tuning_status["message"] = f"微调中... 轮次 {epoch+1}/{epochs}"
            state.set_fine_tuning_status(fine_tuning_status)

            model.train()
            train_loss = 0.0
            correct = 0
            total = 0

            for batch_idx, (data, target) in enumerate(train_loader):
                data, target = data.to(DEVICE), target.to(DEVICE)

                optimizer.zero_grad()
                output = model(data)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()
                _, predicted = output.max(1)
                total += target.size(0)
                correct += predicted.eq(target).sum().item()

                if batch_idx % 10 == 0:
                    print(
                        f"Epoch {epoch+1}, Batch {batch_idx}, Loss: {loss.item():.4f}"
                    )

            train_acc = 100.0 * correct / total
            print(
                f"Epoch {epoch+1}: Train Loss: {train_loss/len(train_loader):.4f}, Train Acc: {train_acc:.2f}%"
            )

        # 保存微调后的模型
        fine_tuning_status["message"] = "保存微调后的模型..."
        state.set_fine_tuning_status(fine_tuning_status)
        model_path = model_paths[model_key]["path"]

        # 备份原始模型
        backup_path = model_path.replace(".pth", "_backup.pth")
        shutil.copy2(model_path, backup_path)

        # 保存微调后的模型
        torch.save(model.state_dict(), model_path)
        print(f"微调后的模型已保存到: {model_path}")

        # 更新旧数据特征
        global OLD_FEATURES
        OLD_FEATURES = None  # 重置，下次检查时会重新提取

        # 清理合并的数据目录
        if os.path.exists(combined_data_path):
            shutil.rmtree(combined_data_path)

        # 清理新数据目录
        if os.path.exists(new_dataset_path):
            shutil.rmtree(new_dataset_path)
            os.makedirs(new_dataset_path, exist_ok=True)
            # 重新创建类别目录
            for class_name in classes:
                os.makedirs(os.path.join(new_dataset_path, class_name), exist_ok=True)

        fine_tuning_status["message"] = "微调完成"
        fine_tuning_status["running"] = False
        state.set_fine_tuning_status(fine_tuning_status)
        return True

    except Exception as e:
        print(f"微调过程中出错: {e}")
        import traceback

        traceback.print_exc()

        fine_tuning_status["message"] = f"微调失败: {str(e)}"
        fine_tuning_status["running"] = False
        state.set_fine_tuning_status(fine_tuning_status)
        return False


def perform_distillation(
    teacher_model_type="resnet50", student_model_type="mobilenetv2", epochs=10
):
    """
    执行知识蒸馏

    Args:
        teacher_model_type: 教师模型类型
        student_model_type: 学生模型类型
        epochs: 蒸馏轮数

    Returns:
        是否蒸馏成功
    """
    try:
        print(f"开始蒸馏: 教师模型={teacher_model_type}, 学生模型={student_model_type}")

        # 加载教师模型
        teacher_key = f"{teacher_model_type}-原始"
        if not load_model(teacher_key):
            print("加载教师模型失败")
            return False

        state = global_state
        models = state.get_models()
        classes = state.get_classes()
        current_run_dir = state.get_current_run_dir()

        teacher_model = models[teacher_key]
        teacher_model.eval()

        # 创建学生模型
        num_classes = len(classes)
        student_model = create_student_model(
            student_model_type, num_classes=num_classes, pretrained=True
        )
        student_model.to(DEVICE)
        student_model.train()

        # 创建数据加载器（使用旧数据）
        train_loader, val_loader, test_loader, classes = create_dataloaders(
            data_dir="old_dataset", batch_size=64, train_ratio=0.8, val_ratio=0.1
        )

        # 设置优化器和损失函数
        optimizer = optim.Adam(student_model.parameters(), lr=1e-4)
        criterion = nn.CrossEntropyLoss()
        temperature = 4.0
        alpha = 0.3

        # 蒸馏训练
        for epoch in range(epochs):
            student_model.train()
            train_loss = 0.0

            for batch_idx, (data, target) in enumerate(train_loader):
                data, target = data.to(DEVICE), target.to(DEVICE)

                optimizer.zero_grad()

                # 教师模型前向传播
                with torch.no_grad():
                    teacher_outputs = teacher_model(data)

                # 学生模型前向传播
                student_outputs = student_model(data)

                # 计算蒸馏损失
                soft_targets = F.softmax(teacher_outputs / temperature, dim=1)
                soft_prob = F.log_softmax(student_outputs / temperature, dim=1)

                soft_targets_loss = (
                    -torch.sum(soft_targets * soft_prob) / student_outputs.size()[0]
                )
                label_loss = criterion(student_outputs, target)

                loss = alpha * soft_targets_loss + (1 - alpha) * label_loss
                loss.backward()
                optimizer.step()

                train_loss += loss.item()

                if batch_idx % 20 == 0:
                    print(
                        f"蒸馏 Epoch {epoch+1}, Batch {batch_idx}, Loss: {loss.item():.4f}"
                    )

        # 保存蒸馏后的模型到checkpoints目录
        distilled_dir = os.path.join(current_run_dir, teacher_model_type, "distilled")
        os.makedirs(distilled_dir, exist_ok=True)

        distilled_model_path = os.path.join(
            distilled_dir, f"student_{student_model_type}.pth"
        )
        torch.save(student_model.state_dict(), distilled_model_path)

        print(f"蒸馏模型已保存到: {distilled_model_path}")

        # 重新发现模型以更新MODEL_PATHS
        discover_available_models(current_run_dir)

        return True

    except Exception as e:
        print(f"蒸馏过程中出错: {e}")
        import traceback

        traceback.print_exc()
        return False


def adaptive_monitoring_thread():
    """
    自适应监控线程函数
    """
    global LAST_CHECK_TIME

    state = global_state

    while state.get_adaptive_monitoring():
        try:
            current_time = datetime.datetime.now()

            # 检查是否到了检测时间（10分钟）
            if (
                LAST_CHECK_TIME is None
                or (current_time - LAST_CHECK_TIME).total_seconds() >= 600
            ):
                print(f"[{current_time}] 开始自适应监控检查...")

                # 检查数据分布
                needs_fine_tuning, diff_score = check_data_distribution()

                if needs_fine_tuning:
                    print("检测到分布差异超过阈值，开始自动微调...")
                    fine_tune_success = fine_tune_model()

                    if fine_tune_success:
                        print("自动微调完成")
                    else:
                        print("自动微调失败")
                else:
                    print("分布差异未超过阈值，继续监控...")

                LAST_CHECK_TIME = current_time

            # 等待30秒后再次检查
            time.sleep(30)

        except Exception as e:
            print(f"自适应监控线程出错: {e}")
            time.sleep(60)  # 出错时等待更长时间


def start_adaptive_monitoring():
    """
    启动自适应监控

    Returns:
        启动状态消息
    """
    state = global_state

    if state.get_adaptive_monitoring():
        return "自适应监控已在运行中"

    state.set_adaptive_monitoring(True)
    monitoring_thread = threading.Thread(target=adaptive_monitoring_thread, daemon=True)
    monitoring_thread.start()

    return "自适应监控已启动，将每10分钟检查一次数据分布变化"


def stop_adaptive_monitoring():
    """
    停止自适应监控

    Returns:
        停止状态消息
    """
    state = global_state
    state.set_adaptive_monitoring(False)
    return "自适应监控已停止"


def manual_fine_tune():
    """
    手动触发微调

    Returns:
        微调状态消息
    """
    state = global_state
    fine_tuning_status = state.get_fine_tuning_status()

    if fine_tuning_status["running"]:
        return "微调正在进行中，请等待完成"

    # 在新线程中执行微调
    def fine_tune_thread():
        fine_tune_model()

    thread = threading.Thread(target=fine_tune_thread, daemon=True)
    thread.start()

    return "手动微调已启动"


def get_fine_tuning_status():
    """
    获取微调状态

    Returns:
        当前微调状态
    """
    state = global_state
    fine_tuning_status = state.get_fine_tuning_status()
    return fine_tuning_status["message"]
