/**
 * 遥感场景分类项目 - 主JavaScript文件
 * 整合所有功能模块，提供统一的应用入口和全局状态管理
 */

// 全局应用状态
const AppState = {
    currentTab: 'adaptive-tuning',
    currentEvalTab: {},
    isInitialized: false,
    modules: {},

    // 应用配置
    config: {
        apiBaseURL: '',
        pollingInterval: 5000,
        maxRetries: 3,
        cacheTimeout: 300000 // 5分钟
    }
};

// 应用主类
class RemoteSensingApp {
    constructor() {
        this.state = AppState;
        this.initPromise = null;
    }

    /**
     * 应用初始化
     */
    async initialize() {
        if (this.initPromise) {
            return this.initPromise;
        }

        this.initPromise = this._doInitialize();
        return this.initPromise;
    }

    /**
     * 执行初始化
     */
    async _doInitialize() {
        try {
            console.log('🚀 遥感场景分类应用开始初始化...');

            // 1. 初始化基础UI组件
            this.initTabs();
            this.initEvalTabs();
            this.initGlobalEventListeners();

            // 2. 检查系统健康状态
            await this.checkSystemHealth();

            // 3. 初始化各功能模块
            await this.initializeModules();

            // 4. 加载初始数据
            await this.loadInitialData();

            // 5. 设置全局错误处理
            this.setupGlobalErrorHandling();

            this.state.isInitialized = true;
            console.log('✅ 应用初始化完成');

            // 显示成功消息
            this.showWelcomeMessage();

        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this.showInitializationError(error);
        }
    }

    /**
     * 检查系统健康状态
     */
    async checkSystemHealth() {
        try {
            const health = await API.getHealth();
            console.log('系统健康检查:', health);

            // 更新状态栏
            this.updateSystemStatus('系统正常运行');
        } catch (error) {
            console.warn('系统健康检查失败:', error);
            this.updateSystemStatus('系统连接异常');
            throw new Error('无法连接到后端服务');
        }
    }

    /**
     * 初始化功能模块
     */
    async initializeModules() {
        console.log('初始化功能模块...');

        // 模块已经通过独立文件创建，这里只需要确保它们正确初始化
        if (window.predictionManager) {
            this.state.modules.prediction = window.predictionManager;
            await this.state.modules.prediction.loadAvailableModels();
        }

        if (window.adaptiveManager) {
            this.state.modules.adaptive = window.adaptiveManager;
        }

        if (window.comparisonManager) {
            this.state.modules.comparison = window.comparisonManager;
        }

        console.log('功能模块初始化完成');
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        console.log('加载初始数据...');

        try {
            // 并行加载各种初始数据
            const promises = [];

            // 加载系统信息
            promises.push(this.loadSystemInfo());

            // 等待所有数据加载完成
            await Promise.allSettled(promises);

            console.log('初始数据加载完成');
        } catch (error) {
            console.error('加载初始数据失败:', error);
        }
    }

    /**
     * 加载系统信息
     */
    async loadSystemInfo() {
        try {
            const info = await API.getInfo();
            if (info.success) {
                this.updateModelDirectory(info.model_directory);
            }
        } catch (error) {
            console.error('加载系统信息失败:', error);
        }
    }

    /**
     * 初始化标签页功能
     */
    initTabs() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', (event) => {
                const targetTab = event.target.getAttribute('data-tab');

                // 移除所有活动状态
                tabBtns.forEach(b => b.classList.remove('active'));
                tabPanes.forEach(p => p.classList.remove('active'));

                // 添加活动状态
                event.target.classList.add('active');
                const targetPane = document.getElementById(targetTab);
                if (targetPane) {
                    targetPane.classList.add('active');
                }

                // 更新状态
                this.state.currentTab = targetTab;

                // 触发标签页切换事件
                this.onTabSwitch(targetTab);

                console.log('切换到标签页:', targetTab);
            });
        });
    }

    /**
     * 初始化评估标签页功能
     */
    initEvalTabs() {
        const evalTabBtns = document.querySelectorAll('.eval-tab-btn');

        evalTabBtns.forEach(btn => {
            btn.addEventListener('click', (event) => {
                const targetTab = event.target.getAttribute('data-eval-tab');
                const parentContainer = event.target.closest('.eval-tabs');

                if (!parentContainer) return;

                // 移除同一容器内的所有活动状态
                parentContainer.querySelectorAll('.eval-tab-btn').forEach(b => b.classList.remove('active'));
                parentContainer.querySelectorAll('.eval-tab-pane').forEach(p => p.classList.remove('active'));

                // 添加活动状态
                event.target.classList.add('active');
                const targetPane = document.getElementById(targetTab);
                if (targetPane) {
                    targetPane.classList.add('active');
                }

                // 更新状态
                const parentTabId = parentContainer.closest('.tab-pane').id;
                this.state.currentEvalTab[parentTabId] = targetTab;

                console.log('切换到评估标签页:', targetTab);
            });
        });
    }

    /**
     * 标签页切换事件处理
     */
    onTabSwitch(tabId) {
        // 根据不同标签页执行相应的初始化操作
        switch (tabId) {
            case 'adaptive-tuning':
                // 自适应微调标签页已激活
                break;
            case 'model-prediction':
                // 模型预测标签页已激活
                break;
            case 'densenet-eval':
            case 'resnet-eval':
            case 'vit-eval':
            case 'swin-eval':
                // 模型评估标签页已激活
                if (this.state.modules.comparison) {
                    this.state.modules.comparison.loadModelEvaluations(tabId);
                }
                break;
        }
    }

    /**
     * 初始化全局事件监听器
     */
    initGlobalEventListeners() {
        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));

        // 窗口大小变化
        window.addEventListener('resize', () => this.handleWindowResize());

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());

        // 网络状态变化
        window.addEventListener('online', () => this.handleNetworkStatusChange(true));
        window.addEventListener('offline', () => this.handleNetworkStatusChange(false));
    }

    /**
     * 处理键盘快捷键
     */
    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + R: 刷新当前标签页数据
        if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
            event.preventDefault();
            this.refreshCurrentTabData();
        }

        // Esc: 关闭模态框或取消当前操作
        if (event.key === 'Escape') {
            this.handleEscapeKey();
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        // 响应式布局调整
        console.log('窗口大小已变化');
    }

    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('页面已隐藏，暂停轮询');
            // 暂停轮询等操作
            if (this.state.modules.adaptive) {
                this.state.modules.adaptive.stopStatusPolling();
            }
        } else {
            console.log('页面已显示，恢复轮询');
            // 恢复轮询等操作
            if (this.state.modules.adaptive && this.state.modules.adaptive.isMonitoring) {
                this.state.modules.adaptive.startStatusPolling();
            }
        }
    }

    /**
     * 处理网络状态变化
     */
    handleNetworkStatusChange(isOnline) {
        if (isOnline) {
            console.log('网络已连接');
            this.updateSystemStatus('网络已连接');
            // 重新检查系统健康状态
            this.checkSystemHealth();
        } else {
            console.log('网络已断开');
            this.updateSystemStatus('网络连接断开');
        }
    }

    /**
     * 刷新当前标签页数据
     */
    async refreshCurrentTabData() {
        const currentTab = this.state.currentTab;

        try {
            showLoading();

            switch (currentTab) {
                case 'adaptive-tuning':
                    if (this.state.modules.adaptive) {
                        await this.state.modules.adaptive.refreshStatus();
                        await this.state.modules.adaptive.refreshDatasetImages();
                    }
                    break;
                case 'model-prediction':
                    if (this.state.modules.prediction) {
                        await this.state.modules.prediction.loadAvailableModels();
                    }
                    break;
                default:
                    if (currentTab.includes('-eval') && this.state.modules.comparison) {
                        await this.state.modules.comparison.refreshAllComparisons();
                    }
                    break;
            }

            showSuccess('数据已刷新');
        } catch (error) {
            console.error('刷新数据失败:', error);
            showError('刷新数据失败');
        } finally {
            hideLoading();
        }
    }

    /**
     * 处理Esc键
     */
    handleEscapeKey() {
        // 关闭加载覆盖层
        hideLoading();

        // 关闭可能打开的模态框
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            modal.classList.remove('show');
        });
    }

    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        // 捕获未处理的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise错误:', event.reason);
            this.handleGlobalError(event.reason);
        });

        // 捕获JavaScript运行时错误
        window.addEventListener('error', (event) => {
            console.error('JavaScript错误:', event.error);
            this.handleGlobalError(event.error);
        });
    }

    /**
     * 处理全局错误
     */
    handleGlobalError(error) {
        // 避免显示过多错误提示
        if (this.lastErrorTime && Date.now() - this.lastErrorTime < 5000) {
            return;
        }

        this.lastErrorTime = Date.now();

        // 显示用户友好的错误信息
        if (error.message && !error.message.includes('Script error')) {
            showError('应用出现错误，请刷新页面重试');
        }
    }

    /**
     * 更新系统状态
     */
    updateSystemStatus(status) {
        const statusElement = document.getElementById('current-model-dir');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    /**
     * 更新模型目录显示
     */
    updateModelDirectory(directory) {
        const statusElement = document.getElementById('current-model-dir');
        if (statusElement) {
            statusElement.textContent = directory || '未知';
        }
    }

    /**
     * 显示欢迎消息
     */
    showWelcomeMessage() {
        // 延迟显示，确保页面完全加载
        setTimeout(() => {
            showSuccess('遥感场景分类系统已就绪');
        }, 1000);
    }

    /**
     * 显示初始化错误
     */
    showInitializationError(error) {
        const errorMessage = `
            <div class="init-error">
                <h3>系统初始化失败</h3>
                <p>错误信息: ${error.message}</p>
                <p>请检查网络连接或联系管理员</p>
                <button onclick="location.reload()" class="btn btn-primary">
                    <i class="fas fa-refresh"></i>
                    重新加载
                </button>
            </div>
        `;

        const container = document.querySelector('.main-content');
        if (container) {
            container.innerHTML = errorMessage;
        }
    }

    /**
     * 获取应用状态
     */
    getState() {
        return this.state;
    }

    /**
     * 获取模块实例
     */
    getModule(moduleName) {
        return this.state.modules[moduleName];
    }
}

}

// 创建全局应用实例
const app = new RemoteSensingApp();

// 全局工具函数
/**
 * 显示加载覆盖层
 */
function showLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.display = 'flex';
    }
}

/**
 * 隐藏加载覆盖层
 */
function hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

/**
 * 显示错误消息
 */
function showError(message) {
    console.error('错误:', message);

    // 创建错误提示元素
    const errorDiv = document.createElement('div');
    errorDiv.className = 'toast toast-error';
    errorDiv.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-exclamation-circle"></i>
            <span>${message}</span>
            <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(errorDiv);

    // 自动移除
    setTimeout(() => {
        if (errorDiv.parentElement) {
            errorDiv.remove();
        }
    }, 5000);
}

/**
 * 显示成功消息
 */
function showSuccess(message) {
    console.log('成功:', message);

    // 创建成功提示元素
    const successDiv = document.createElement('div');
    successDiv.className = 'toast toast-success';
    successDiv.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
            <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(successDiv);

    // 自动移除
    setTimeout(() => {
        if (successDiv.parentElement) {
            successDiv.remove();
        }
    }, 3000);
}

/**
 * 显示警告消息
 */
function showWarning(message) {
    console.warn('警告:', message);

    // 创建警告提示元素
    const warningDiv = document.createElement('div');
    warningDiv.className = 'toast toast-warning';
    warningDiv.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-exclamation-triangle"></i>
            <span>${message}</span>
            <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(warningDiv);

    // 自动移除
    setTimeout(() => {
        if (warningDiv.parentElement) {
            warningDiv.remove();
        }
    }, 4000);
}

/**
 * 格式化时间戳
 */
function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 防抖函数
 */
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', async function() {
    console.log('DOM加载完成，开始初始化应用...');

    try {
        await app.initialize();
    } catch (error) {
        console.error('应用初始化失败:', error);
    }
});

// 导出全局对象
window.app = app;
window.showLoading = showLoading;
window.hideLoading = hideLoading;
window.showError = showError;
window.showSuccess = showSuccess;
window.showWarning = showWarning;
window.formatTimestamp = formatTimestamp;
window.formatFileSize = formatFileSize;
window.debounce = debounce;
window.throttle = throttle;
