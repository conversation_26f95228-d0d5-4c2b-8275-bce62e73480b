"""
FastAPI依赖注入和中间件配置

提供全局依赖、中间件和配置管理功能。
"""

import os
import logging
from typing import Optional
from fastapi import HTTPException, status
from fastapi.security import HTTPBearer
import threading

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局锁，用于线程安全
global_lock = threading.Lock()

# 安全认证（可选）
security = HTTPBearer(auto_error=False)


def get_current_working_directory():
    """获取当前工作目录"""
    return os.getcwd()


def verify_file_path(file_path: str) -> bool:
    """
    验证文件路径的安全性，防止路径遍历攻击
    
    Args:
        file_path: 要验证的文件路径
        
    Returns:
        bool: 路径是否安全
    """
    # 获取绝对路径
    abs_path = os.path.abspath(file_path)
    
    # 获取项目根目录
    project_root = os.path.abspath(os.getcwd())
    
    # 检查路径是否在项目根目录下
    return abs_path.startswith(project_root)


def get_upload_directory():
    """获取文件上传目录"""
    upload_dir = os.path.join(os.getcwd(), "imgs")
    os.makedirs(upload_dir, exist_ok=True)
    return upload_dir


class GlobalState:
    """全局状态管理类"""
    
    def __init__(self):
        self._models = {}
        self._model_infos = {}
        self._model_paths = {}
        self._classes = []
        self._current_run_dir = ""
        self._adaptive_monitoring = False
        self._fine_tuning_status = {"running": False, "message": ""}
        
    def get_models(self):
        with global_lock:
            return self._models.copy()
    
    def set_models(self, models):
        with global_lock:
            self._models = models
            
    def get_model_paths(self):
        with global_lock:
            return self._model_paths.copy()
    
    def set_model_paths(self, model_paths):
        with global_lock:
            self._model_paths = model_paths
            
    def get_classes(self):
        with global_lock:
            return self._classes.copy()
    
    def set_classes(self, classes):
        with global_lock:
            self._classes = classes
            
    def get_current_run_dir(self):
        with global_lock:
            return self._current_run_dir
    
    def set_current_run_dir(self, run_dir):
        with global_lock:
            self._current_run_dir = run_dir
            
    def get_adaptive_monitoring(self):
        with global_lock:
            return self._adaptive_monitoring
    
    def set_adaptive_monitoring(self, status):
        with global_lock:
            self._adaptive_monitoring = status
            
    def get_fine_tuning_status(self):
        with global_lock:
            return self._fine_tuning_status.copy()
    
    def set_fine_tuning_status(self, status):
        with global_lock:
            self._fine_tuning_status = status


# 全局状态实例
global_state = GlobalState()


def get_global_state() -> GlobalState:
    """获取全局状态实例"""
    return global_state


def handle_api_error(error: Exception) -> HTTPException:
    """
    统一处理API错误
    
    Args:
        error: 异常对象
        
    Returns:
        HTTPException: 格式化的HTTP异常
    """
    logger.error(f"API错误: {str(error)}")
    
    if isinstance(error, FileNotFoundError):
        return HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"文件未找到: {str(error)}"
        )
    elif isinstance(error, PermissionError):
        return HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"权限不足: {str(error)}"
        )
    elif isinstance(error, ValueError):
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"参数错误: {str(error)}"
        )
    else:
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"内部服务器错误: {str(error)}"
        )
