/**
 * 模型比较功能模块
 * 处理模型性能比较、表格展示和数据更新
 */

class ComparisonManager {
    constructor() {
        this.comparisonCache = new Map();
        this.currentComparisons = {};
        
        this.initializeElements();
        this.bindEvents();
        this.loadInitialComparisons();
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        this.elements = {
            // DenseNet201评估结果容器
            densenetPruningResults: document.getElementById('densenet-pruning-results'),
            densenetDistillationResults: document.getElementById('densenet-distillation-results'),
            
            // ResNet50评估结果容器
            resnetPruningResults: document.getElementById('resnet-pruning-results'),
            resnetDistillationResults: document.getElementById('resnet-distillation-results'),
            resnetQuantizationResults: document.getElementById('resnet-quantization-results'),
            
            // ViT-S/16评估结果容器
            vitPruningResults: document.getElementById('vit-pruning-results'),
            vitDistillationResults: document.getElementById('vit-distillation-results'),
            
            // Swin-T评估结果容器
            swinPruningResults: document.getElementById('swin-pruning-results'),
            swinDistillationResults: document.getElementById('swin-distillation-results')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 监听评估标签页切换
        const evalTabBtns = document.querySelectorAll('.eval-tab-btn');
        evalTabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleEvalTabSwitch(e));
        });

        // 监听主标签页切换
        const tabBtns = document.querySelectorAll('.tab-btn');
        tabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleMainTabSwitch(e));
        });
    }

    /**
     * 处理评估标签页切换
     */
    handleEvalTabSwitch(event) {
        const targetTab = event.target.getAttribute('data-eval-tab');
        const parentContainer = event.target.closest('.eval-tabs');
        const tabPaneId = targetTab;
        
        // 延迟加载内容，确保标签页切换动画完成
        setTimeout(() => {
            this.loadComparisonContent(tabPaneId);
        }, 100);
    }

    /**
     * 处理主标签页切换
     */
    handleMainTabSwitch(event) {
        const targetTab = event.target.getAttribute('data-tab');
        
        // 如果切换到评估相关的标签页，加载对应内容
        if (targetTab && targetTab.includes('-eval')) {
            setTimeout(() => {
                this.loadModelEvaluations(targetTab);
            }, 100);
        }
    }

    /**
     * 加载初始比较数据
     */
    async loadInitialComparisons() {
        try {
            // 获取比较功能概览
            const overview = await API.getComparisonOverview();
            if (overview.success) {
                console.log('比较功能概览:', overview);
            }
        } catch (error) {
            console.error('加载比较概览失败:', error);
        }
    }

    /**
     * 加载模型评估数据
     */
    async loadModelEvaluations(tabId) {
        const modelType = this.getModelTypeFromTabId(tabId);
        if (!modelType) return;

        // 加载该模型的所有比较类型
        await this.loadPruningComparison(modelType);
        await this.loadDistillationComparison(modelType);
        
        // ResNet50还支持量化比较
        if (modelType === 'resnet50') {
            await this.loadQuantizationComparison(modelType);
        }
    }

    /**
     * 从标签页ID获取模型类型
     */
    getModelTypeFromTabId(tabId) {
        const mapping = {
            'densenet-eval': 'densenet201',
            'resnet-eval': 'resnet50',
            'vit-eval': 'vit_s_16',
            'swin-eval': 'swin_t'
        };
        return mapping[tabId];
    }

    /**
     * 加载比较内容
     */
    async loadComparisonContent(tabPaneId) {
        const [modelPrefix, comparisonType] = this.parseTabPaneId(tabPaneId);
        if (!modelPrefix || !comparisonType) return;

        const modelType = this.getModelTypeFromPrefix(modelPrefix);
        if (!modelType) return;

        switch (comparisonType) {
            case 'pruning':
                await this.loadPruningComparison(modelType);
                break;
            case 'distillation':
                await this.loadDistillationComparison(modelType);
                break;
            case 'quantization':
                if (modelType === 'resnet50') {
                    await this.loadQuantizationComparison(modelType);
                }
                break;
        }
    }

    /**
     * 解析标签页面板ID
     */
    parseTabPaneId(tabPaneId) {
        // 例如: "densenet-pruning" -> ["densenet", "pruning"]
        const parts = tabPaneId.split('-');
        if (parts.length >= 2) {
            return [parts[0], parts[1]];
        }
        return [null, null];
    }

    /**
     * 从前缀获取模型类型
     */
    getModelTypeFromPrefix(prefix) {
        const mapping = {
            'densenet': 'densenet201',
            'resnet': 'resnet50',
            'vit': 'vit_s_16',
            'swin': 'swin_t'
        };
        return mapping[prefix];
    }

    /**
     * 加载剪枝比较
     */
    async loadPruningComparison(modelType) {
        const cacheKey = `pruning-${modelType}`;
        
        // 检查缓存
        if (this.comparisonCache.has(cacheKey)) {
            const cachedData = this.comparisonCache.get(cacheKey);
            this.displayComparison(modelType, 'pruning', cachedData);
            return;
        }

        try {
            const containerId = `${this.getModelPrefix(modelType)}-pruning-results`;
            this.showLoadingState(containerId);

            const response = await API.getPruningComparison(modelType);
            
            if (response.success) {
                this.comparisonCache.set(cacheKey, response.comparison);
                this.displayComparison(modelType, 'pruning', response.comparison);
            } else {
                throw new Error(response.message || '获取剪枝比较失败');
            }
        } catch (error) {
            console.error(`加载${modelType}剪枝比较失败:`, error);
            this.showErrorState(`${this.getModelPrefix(modelType)}-pruning-results`, error.message);
        }
    }

    /**
     * 加载蒸馏比较
     */
    async loadDistillationComparison(modelType) {
        const cacheKey = `distillation-${modelType}`;
        
        if (this.comparisonCache.has(cacheKey)) {
            const cachedData = this.comparisonCache.get(cacheKey);
            this.displayComparison(modelType, 'distillation', cachedData);
            return;
        }

        try {
            const containerId = `${this.getModelPrefix(modelType)}-distillation-results`;
            this.showLoadingState(containerId);

            const response = await API.getDistillationComparison(modelType);
            
            if (response.success) {
                this.comparisonCache.set(cacheKey, response.comparison);
                this.displayComparison(modelType, 'distillation', response.comparison);
            } else {
                throw new Error(response.message || '获取蒸馏比较失败');
            }
        } catch (error) {
            console.error(`加载${modelType}蒸馏比较失败:`, error);
            this.showErrorState(`${this.getModelPrefix(modelType)}-distillation-results`, error.message);
        }
    }

    /**
     * 加载量化比较
     */
    async loadQuantizationComparison(modelType) {
        if (modelType !== 'resnet50') return;

        const cacheKey = `quantization-${modelType}`;
        
        if (this.comparisonCache.has(cacheKey)) {
            const cachedData = this.comparisonCache.get(cacheKey);
            this.displayComparison(modelType, 'quantization', cachedData);
            return;
        }

        try {
            const containerId = `${this.getModelPrefix(modelType)}-quantization-results`;
            this.showLoadingState(containerId);

            const response = await API.getQuantizationComparison(modelType);
            
            if (response.success) {
                this.comparisonCache.set(cacheKey, response.comparison);
                this.displayComparison(modelType, 'quantization', response.comparison);
            } else {
                throw new Error(response.message || '获取量化比较失败');
            }
        } catch (error) {
            console.error(`加载${modelType}量化比较失败:`, error);
            this.showErrorState(`${this.getModelPrefix(modelType)}-quantization-results`, error.message);
        }
    }

    /**
     * 获取模型前缀
     */
    getModelPrefix(modelType) {
        const mapping = {
            'densenet201': 'densenet',
            'resnet50': 'resnet',
            'vit_s_16': 'vit',
            'swin_t': 'swin'
        };
        return mapping[modelType];
    }

    /**
     * 显示比较结果
     */
    displayComparison(modelType, comparisonType, comparison) {
        const containerId = `${this.getModelPrefix(modelType)}-${comparisonType}-results`;
        const container = document.getElementById(containerId);
        
        if (!container) {
            console.warn(`容器不存在: ${containerId}`);
            return;
        }

        // 显示HTML内容
        if (comparison.html_content) {
            container.innerHTML = `
                <div class="comparison-header">
                    <h4>${comparison.title}</h4>
                </div>
                <div class="comparison-content">
                    ${comparison.html_content}
                </div>
            `;
        } else {
            container.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-info-circle"></i>
                    <span>暂无${this.getComparisonTypeName(comparisonType)}比较数据</span>
                </div>
            `;
        }
    }

    /**
     * 获取比较类型名称
     */
    getComparisonTypeName(comparisonType) {
        const mapping = {
            'pruning': '剪枝',
            'distillation': '蒸馏',
            'quantization': '量化'
        };
        return mapping[comparisonType] || comparisonType;
    }

    /**
     * 显示加载状态
     */
    showLoadingState(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = `
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                <span>加载比较结果...</span>
            </div>
        `;
    }

    /**
     * 显示错误状态
     */
    showErrorState(containerId, errorMessage) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <span>加载失败: ${errorMessage}</span>
                <button class="btn btn-secondary" onclick="location.reload()">
                    <i class="fas fa-refresh"></i>
                    重试
                </button>
            </div>
        `;
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.comparisonCache.clear();
        console.log('比较结果缓存已清除');
    }

    /**
     * 刷新所有比较数据
     */
    async refreshAllComparisons() {
        this.clearCache();
        
        // 重新加载当前可见的比较数据
        const activeTab = document.querySelector('.tab-btn.active');
        if (activeTab) {
            const targetTab = activeTab.getAttribute('data-tab');
            if (targetTab && targetTab.includes('-eval')) {
                await this.loadModelEvaluations(targetTab);
            }
        }
        
        showSuccess('比较数据已刷新');
    }
}

// 创建全局比较管理器实例
window.comparisonManager = new ComparisonManager();
