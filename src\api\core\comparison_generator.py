"""
模型比较功能模块

从原app.py提取的模型比较相关功能，包括剪枝比较、蒸馏比较、量化比较等。
保持所有原有功能的完整性，并确保线程安全。
"""

import os
import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.api.dependencies import global_state, global_lock


def create_pruning_comparison(model_type):
    """
    创建原始模型与剪枝模型的比较表格

    Args:
        model_type: 模型类型 (densenet201, resnet50, swin_t, vit_s_16)

    Returns:
        HTML表格
    """
    state = global_state
    current_run_dir = state.get_current_run_dir()

    if not current_run_dir:
        return "未找到运行目录，请先加载模型"

    # 获取模型信息
    with global_lock:
        if not hasattr(state, "_model_infos"):
            return f"未找到 {model_type} 的模型信息"
        model_infos = state._model_infos

    if model_type not in model_infos:
        return f"未找到 {model_type} 的模型信息"

    model_info = model_infos[model_type]

    # 检查是否存在原始模型和剪枝模型
    if not model_info["original"]:
        return f"未找到 {model_type} 的原始模型信息"

    if not model_info["pruned"]:
        return f"未找到 {model_type} 的剪枝模型信息"

    orig_info = model_info["original"]
    orig_metrics = orig_info["metrics"]

    pruned_info = model_info["pruned"]
    pruned_metrics = pruned_info["metrics"]
    comparison = pruned_info.get("comparison", {})

    # 非零参数量和稀疏度
    nonzero_params_orig = f"{orig_metrics.get('parameter_count', 0):,}"
    nonzero_params_pruned = "N/A"
    sparsity_orig = "0.0000"
    sparsity_pruned = "N/A"
    rel_nonzero = "N/A"

    if "Original" in comparison and "Pruned" in comparison:
        original_comp = comparison["Original"]
        pruned_comp = comparison["Pruned"]
        nonzero_params_orig = original_comp["nonzero_params"]
        nonzero_params_pruned = pruned_comp["nonzero_params"]
        sparsity_orig = original_comp["sparsity"]
        sparsity_pruned = pruned_comp["sparsity"]
        rel_nonzero = (
            f"{(float(nonzero_params_pruned) / float(nonzero_params_orig) * 100):.2f}%"
        )

    # 创建数据表
    table_data = {
        "评估指标": ["准确率", "精确率", "召回率", "F1分数", "非零参数量", "稀疏度"],
        f"{model_type} (原始)": [
            f"{orig_metrics.get('accuracy', 0.0):.4f}",
            f"{orig_metrics.get('precision', 0.0):.4f}",
            f"{orig_metrics.get('recall', 0.0):.4f}",
            f"{orig_metrics.get('f1', 0.0):.4f}",
            nonzero_params_orig,
            sparsity_orig,
        ],
        f"{model_type} (剪枝)": [
            f"{pruned_metrics.get('accuracy', 0.0):.4f}",
            f"{pruned_metrics.get('precision', 0.0):.4f}",
            f"{pruned_metrics.get('recall', 0.0):.4f}",
            f"{pruned_metrics.get('f1', 0.0):.4f}",
            nonzero_params_pruned,
            sparsity_pruned,
        ],
        "相对变化": [
            f"{(pruned_metrics.get('accuracy', 0.0) / orig_metrics.get('accuracy', 1.0) * 100 - 100):.2f}%",
            f"{(pruned_metrics.get('precision', 0.0) / orig_metrics.get('precision', 1.0) * 100 - 100):.2f}%",
            f"{(pruned_metrics.get('recall', 0.0) / orig_metrics.get('recall', 1.0) * 100 - 100):.2f}%",
            f"{(pruned_metrics.get('f1', 0.0) / orig_metrics.get('f1', 1.0) * 100 - 100):.2f}%",
            rel_nonzero,
            "-",
        ],
    }

    # 创建DataFrame并转换为HTML表格
    df = pd.DataFrame(table_data)
    table_html = df.to_html(
        index=False, classes="pure-table pure-table-striped", border=0
    )

    # 添加CSS样式
    css = """
    <style>
        .pure-table {
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .pure-table th, .pure-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .pure-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .pure-table-striped tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        h3 {
            margin-top: 30px;
            margin-bottom: 10px;
            color: #333;
        }
    </style>
    """

    return css + f"<h3>{model_type.upper()} 剪枝模型与原始模型比较</h3>" + table_html


def create_distillation_comparison(model_type):
    """
    创建原始模型与蒸馏模型的比较表格

    Args:
        model_type: 模型类型 (densenet201, resnet50, swin_t, vit_s_16)

    Returns:
        HTML表格
    """
    state = global_state
    current_run_dir = state.get_current_run_dir()

    if not current_run_dir:
        return "未找到运行目录，请先加载模型"

    # 获取模型信息
    with global_lock:
        if not hasattr(state, "_model_infos"):
            return f"未找到 {model_type} 的模型信息"
        model_infos = state._model_infos

    if model_type not in model_infos:
        return f"未找到 {model_type} 的模型信息"

    model_info = model_infos[model_type]

    # 检查是否存在原始模型和蒸馏模型
    if not model_info["original"]:
        return f"未找到 {model_type} 的原始模型信息"

    if not model_info["distilled"]:
        return f"未找到 {model_type} 的蒸馏模型信息"

    orig_info = model_info["original"]
    orig_metrics = orig_info["metrics"]

    distilled_info = model_info["distilled"]
    distilled_metrics = distilled_info["metrics"]

    # 创建数据表
    table_data = {
        "评估指标": ["准确率", "精确率", "召回率", "F1分数", "参数量", "模型大小(MB)"],
        f"{model_type} (原始)": [
            f"{orig_metrics.get('accuracy', 0.0):.4f}",
            f"{orig_metrics.get('precision', 0.0):.4f}",
            f"{orig_metrics.get('recall', 0.0):.4f}",
            f"{orig_metrics.get('f1', 0.0):.4f}",
            f"{orig_metrics.get('parameter_count', 0):,}",
            f"{orig_info['size_mb']:.2f}",
        ],
        f"{model_type} (蒸馏 - MobileNetV2)": [
            f"{distilled_metrics.get('accuracy', 0.0):.4f}",
            f"{distilled_metrics.get('precision', 0.0):.4f}",
            f"{distilled_metrics.get('recall', 0.0):.4f}",
            f"{distilled_metrics.get('f1', 0.0):.4f}",
            f"{distilled_metrics.get('parameter_count', 0):,}",
            f"{distilled_info['size_mb']:.2f}",
        ],
        "相对变化": [
            f"{(distilled_metrics.get('accuracy', 0.0) / orig_metrics.get('accuracy', 1.0) * 100 - 100):.2f}%",
            f"{(distilled_metrics.get('precision', 0.0) / orig_metrics.get('precision', 1.0) * 100 - 100):.2f}%",
            f"{(distilled_metrics.get('recall', 0.0) / orig_metrics.get('recall', 1.0) * 100 - 100):.2f}%",
            f"{(distilled_metrics.get('f1', 0.0) / orig_metrics.get('f1', 1.0) * 100 - 100):.2f}%",
            f"{((orig_metrics.get('parameter_count', 0) - distilled_metrics.get('parameter_count', 0)) / orig_metrics.get('parameter_count', 1) * 100):.2f}%",
            f"{(orig_info['size_mb'] - distilled_info['size_mb'] / orig_info['size_mb'] * 100):.2f}%",
        ],
    }

    # 创建DataFrame并转换为HTML表格
    df = pd.DataFrame(table_data)
    table_html = df.to_html(
        index=False, classes="pure-table pure-table-striped", border=0
    )

    # 添加CSS样式
    css = """
    <style>
        .pure-table {
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .pure-table th, .pure-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .pure-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .pure-table-striped tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        h3 {
            margin-top: 30px;
            margin-bottom: 10px;
            color: #333;
        }
    </style>
    """

    return css + f"<h3>{model_type.upper()} 蒸馏模型与原始模型比较</h3>" + table_html


def create_quantization_comparison(model_type="resnet50"):
    """
    创建原始模型与量化模型的比较表格 (仅限ResNet50)

    Args:
        model_type: 必须是 "resnet50"

    Returns:
        HTML表格
    """
    if model_type != "resnet50":
        return "量化模型比较仅支持ResNet50"

    state = global_state
    current_run_dir = state.get_current_run_dir()

    if not current_run_dir:
        return "未找到运行目录，请先加载模型"

    # 获取模型信息
    with global_lock:
        if not hasattr(state, "_model_infos"):
            return f"未找到 {model_type} 的模型信息"
        model_infos = state._model_infos

    if model_type not in model_infos:
        return f"未找到 {model_type} 的模型信息"

    model_info = model_infos[model_type]

    # 检查是否存在原始模型和量化模型
    if not model_info["original"]:
        return f"未找到 {model_type} 的原始模型信息"

    if not model_info["quantized"]:
        return f"未找到 {model_type} 的量化模型信息"

    orig_info = model_info["original"]
    orig_metrics = orig_info["metrics"]

    quantized_info = model_info["quantized"]
    quantized_metrics = quantized_info["metrics"]

    # 创建数据表
    table_data = {
        "评估指标": ["准确率", "精确率", "召回率", "F1分数", "模型大小(MB)"],
        f"{model_type} (原始)": [
            f"{orig_metrics.get('accuracy', 0.0):.4f}",
            f"{orig_metrics.get('precision', 0.0):.4f}",
            f"{orig_metrics.get('recall', 0.0):.4f}",
            f"{orig_metrics.get('f1', 0.0):.4f}",
            f"{orig_info['size_mb']:.2f}",
        ],
        f"{model_type} (量化)": [
            f"{quantized_metrics.get('accuracy', 0.0):.4f}",
            f"{quantized_metrics.get('precision', 0.0):.4f}",
            f"{quantized_metrics.get('recall', 0.0):.4f}",
            f"{quantized_metrics.get('f1', 0.0):.4f}",
            f"{quantized_info['size_mb']:.2f}",
        ],
        "相对变化": [
            f"{(quantized_metrics.get('accuracy', 0.0) / orig_metrics.get('accuracy', 1.0) * 100 - 100):.2f}%",
            f"{(quantized_metrics.get('precision', 0.0) / orig_metrics.get('precision', 1.0) * 100 - 100):.2f}%",
            f"{(quantized_metrics.get('recall', 0.0) / orig_metrics.get('recall', 1.0) * 100 - 100):.2f}%",
            f"{(quantized_metrics.get('f1', 0.0) / orig_metrics.get('f1', 1.0) * 100 - 100):.2f}%",
            f"{(orig_info['size_mb'] - quantized_info['size_mb']) / orig_info['size_mb'] * 100:.2f}%",
        ],
    }

    # 创建DataFrame并转换为HTML表格
    df = pd.DataFrame(table_data)
    table_html = df.to_html(
        index=False, classes="pure-table pure-table-striped", border=0
    )

    # 添加CSS样式
    css = """
    <style>
        .pure-table {
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .pure-table th, .pure-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .pure-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .pure-table-striped tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        h3 {
            margin-top: 30px;
            margin-bottom: 10px;
            color: #333;
        }
    </style>
    """

    return css + f"<h3>{model_type.upper()} 量化模型与原始模型比较</h3>" + table_html
