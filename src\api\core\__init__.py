"""
核心业务逻辑模块

包含从原app.py提取的所有核心功能，重构为独立的业务逻辑模块。
保持所有原有功能的完整性，并确保线程安全。
"""

from .model_manager import (
    load_model,
    unload_all_models,
    discover_available_models,
    find_latest_run_directory,
    get_model_info
)

from .image_processor import (
    preprocess_image,
    save_uploaded_image,
    predict,
    get_dataset_sample_images,
    get_dataset_comparison_images
)

from .adaptive_tuning import (
    fine_tune_model,
    perform_distillation,
    check_data_distribution,
    compute_distribution_difference,
    extract_features_from_dataset,
    adaptive_monitoring_thread,
    start_adaptive_monitoring,
    stop_adaptive_monitoring,
    manual_fine_tune,
    get_fine_tuning_status
)

from .comparison_generator import (
    create_pruning_comparison,
    create_distillation_comparison,
    create_quantization_comparison
)

__all__ = [
    # 模型管理
    'load_model',
    'unload_all_models', 
    'discover_available_models',
    'find_latest_run_directory',
    'get_model_info',
    
    # 图像处理
    'preprocess_image',
    'save_uploaded_image',
    'predict',
    'get_dataset_sample_images',
    'get_dataset_comparison_images',
    
    # 自适应微调
    'fine_tune_model',
    'perform_distillation',
    'check_data_distribution',
    'compute_distribution_difference',
    'extract_features_from_dataset',
    'adaptive_monitoring_thread',
    'start_adaptive_monitoring',
    'stop_adaptive_monitoring',
    'manual_fine_tune',
    'get_fine_tuning_status',
    
    # 模型比较
    'create_pruning_comparison',
    'create_distillation_comparison',
    'create_quantization_comparison'
]
