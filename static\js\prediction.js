/**
 * 预测功能模块
 * 处理图片上传、模型选择、预测执行和结果显示
 */

class PredictionManager {
    constructor() {
        this.currentImage = null;
        this.selectedModel = null;
        this.availableModels = [];
        this.predictionHistory = [];
        
        this.initializeElements();
        this.bindEvents();
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        this.elements = {
            imageInput: document.getElementById('image-input'),
            imagePreview: document.getElementById('image-preview'),
            modelSelect: document.getElementById('model-select'),
            predictBtn: document.getElementById('predict-btn'),
            predictionOutput: document.getElementById('prediction-output'),
            timeOutput: document.getElementById('time-output')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        if (this.elements.imageInput) {
            this.elements.imageInput.addEventListener('change', (e) => this.handleImageUpload(e));
        }

        if (this.elements.modelSelect) {
            this.elements.modelSelect.addEventListener('change', (e) => this.handleModelSelection(e));
        }

        if (this.elements.predictBtn) {
            this.elements.predictBtn.addEventListener('click', () => this.performPrediction());
        }
    }

    /**
     * 加载可用模型列表
     */
    async loadAvailableModels() {
        try {
            const response = await API.getAvailableModels();
            
            if (response.success) {
                this.availableModels = response.models;
                this.updateModelSelect();
                console.log('模型列表加载成功:', this.availableModels);
            } else {
                throw new Error(response.message || '获取模型列表失败');
            }
        } catch (error) {
            console.error('加载模型列表失败:', error);
            ErrorHandler.handleAPIError(error, '加载模型列表');
            this.showModelLoadError();
        }
    }

    /**
     * 更新模型选择下拉框
     */
    updateModelSelect() {
        if (!this.elements.modelSelect) return;

        // 清空现有选项
        this.elements.modelSelect.innerHTML = '<option value="">请选择模型...</option>';

        // 添加模型选项
        this.availableModels.forEach(model => {
            const option = document.createElement('option');
            option.value = model.name;
            option.textContent = `${model.display_name} (${model.type})`;
            option.title = model.description || '';
            this.elements.modelSelect.appendChild(option);
        });
    }

    /**
     * 显示模型加载错误
     */
    showModelLoadError() {
        if (!this.elements.modelSelect) return;

        this.elements.modelSelect.innerHTML = '<option value="">模型加载失败，请刷新页面重试</option>';
        this.elements.modelSelect.disabled = true;
    }

    /**
     * 处理图片上传
     */
    handleImageUpload(event) {
        const file = event.target.files[0];
        
        if (!file) {
            this.clearImagePreview();
            this.updatePredictButton();
            return;
        }

        // 验证文件类型
        if (!this.isValidImageFile(file)) {
            showError('请选择有效的图片文件 (JPG, PNG, GIF, BMP)');
            this.clearImagePreview();
            this.updatePredictButton();
            return;
        }

        // 验证文件大小 (最大10MB)
        if (file.size > 10 * 1024 * 1024) {
            showError('图片文件大小不能超过10MB');
            this.clearImagePreview();
            this.updatePredictButton();
            return;
        }

        this.currentImage = file;
        this.showImagePreview(file);
        this.updatePredictButton();
    }

    /**
     * 验证图片文件类型
     */
    isValidImageFile(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp'];
        return validTypes.includes(file.type);
    }

    /**
     * 显示图片预览
     */
    showImagePreview(file) {
        if (!this.elements.imagePreview) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            this.elements.imagePreview.innerHTML = `
                <img src="${e.target.result}" alt="预览图片" style="max-width: 100%; max-height: 300px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                <div style="margin-top: 10px; font-size: 0.9rem; color: #666;">
                    <strong>文件名:</strong> ${file.name}<br>
                    <strong>大小:</strong> ${this.formatFileSize(file.size)}<br>
                    <strong>类型:</strong> ${file.type}
                </div>
            `;
        };
        reader.readAsDataURL(file);
    }

    /**
     * 清除图片预览
     */
    clearImagePreview() {
        if (this.elements.imagePreview) {
            this.elements.imagePreview.innerHTML = '';
        }
        this.currentImage = null;
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 处理模型选择
     */
    handleModelSelection(event) {
        this.selectedModel = event.target.value;
        this.updatePredictButton();
        console.log('选择模型:', this.selectedModel);
    }

    /**
     * 更新预测按钮状态
     */
    updatePredictButton() {
        if (!this.elements.predictBtn) return;

        const canPredict = this.currentImage && this.selectedModel;
        this.elements.predictBtn.disabled = !canPredict;
        
        if (canPredict) {
            this.elements.predictBtn.textContent = '开始预测';
            this.elements.predictBtn.classList.remove('disabled');
        } else {
            this.elements.predictBtn.textContent = '请选择图片和模型';
            this.elements.predictBtn.classList.add('disabled');
        }
    }

    /**
     * 执行预测
     */
    async performPrediction() {
        if (!this.currentImage || !this.selectedModel) {
            showError('请先选择图片和模型');
            return;
        }

        try {
            // 更新按钮状态
            this.elements.predictBtn.disabled = true;
            this.elements.predictBtn.textContent = '预测中...';

            // 记录开始时间
            const startTime = Date.now();

            // 调用预测API
            const response = await API.predict(this.currentImage, this.selectedModel);

            // 计算推理时间
            const inferenceTime = Date.now() - startTime;

            if (response.success) {
                this.displayPredictionResults(response.prediction, inferenceTime);
                this.addToPredictionHistory(response.prediction, inferenceTime);
                showSuccess('预测完成');
            } else {
                throw new Error(response.message || '预测失败');
            }

        } catch (error) {
            console.error('预测失败:', error);
            ErrorHandler.handleAPIError(error, '模型预测');
            this.displayPredictionError(error.message);
        } finally {
            // 恢复按钮状态
            this.elements.predictBtn.disabled = false;
            this.updatePredictButton();
        }
    }

    /**
     * 显示预测结果
     */
    displayPredictionResults(prediction, inferenceTime) {
        if (!this.elements.predictionOutput) return;

        // 显示预测结果
        let resultHTML = '<div class="prediction-results-list">';
        
        if (prediction.probabilities && Array.isArray(prediction.probabilities)) {
            // 按概率排序
            const sortedResults = prediction.probabilities
                .sort((a, b) => b.probability - a.probability)
                .slice(0, 5); // 只显示前5个结果

            sortedResults.forEach((result, index) => {
                const percentage = (result.probability * 100).toFixed(2);
                const isTop = index === 0;
                
                resultHTML += `
                    <div class="prediction-item ${isTop ? 'top-prediction' : ''}">
                        <div class="prediction-label">
                            ${isTop ? '🏆 ' : ''}${result.class_name}
                        </div>
                        <div class="prediction-confidence">
                            ${percentage}%
                        </div>
                        <div class="prediction-bar">
                            <div class="prediction-bar-fill" style="width: ${percentage}%"></div>
                        </div>
                    </div>
                `;
            });
        } else {
            // 简单结果显示
            resultHTML += `
                <div class="prediction-item top-prediction">
                    <div class="prediction-label">
                        🏆 ${prediction.predicted_class}
                    </div>
                    <div class="prediction-confidence">
                        ${(prediction.confidence * 100).toFixed(2)}%
                    </div>
                </div>
            `;
        }
        
        resultHTML += '</div>';
        this.elements.predictionOutput.innerHTML = resultHTML;

        // 显示推理时间
        if (this.elements.timeOutput) {
            this.elements.timeOutput.textContent = `${inferenceTime} ms`;
        }
    }

    /**
     * 显示预测错误
     */
    displayPredictionError(errorMessage) {
        if (!this.elements.predictionOutput) return;

        this.elements.predictionOutput.innerHTML = `
            <div class="prediction-error">
                <i class="fas fa-exclamation-triangle"></i>
                <span>预测失败: ${errorMessage}</span>
            </div>
        `;

        if (this.elements.timeOutput) {
            this.elements.timeOutput.textContent = '--';
        }
    }

    /**
     * 添加到预测历史
     */
    addToPredictionHistory(prediction, inferenceTime) {
        const historyItem = {
            timestamp: new Date(),
            image: this.currentImage.name,
            model: this.selectedModel,
            prediction: prediction,
            inferenceTime: inferenceTime
        };

        this.predictionHistory.unshift(historyItem);
        
        // 限制历史记录数量
        if (this.predictionHistory.length > 10) {
            this.predictionHistory = this.predictionHistory.slice(0, 10);
        }

        console.log('预测历史已更新:', this.predictionHistory);
    }

    /**
     * 获取预测历史
     */
    getPredictionHistory() {
        return this.predictionHistory;
    }

    /**
     * 清除预测历史
     */
    clearPredictionHistory() {
        this.predictionHistory = [];
        console.log('预测历史已清除');
    }
}

// 创建全局预测管理器实例
window.predictionManager = new PredictionManager();
