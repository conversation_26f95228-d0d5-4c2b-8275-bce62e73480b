/**
 * 自适应微调功能模块
 * 处理监控控制、状态更新、数据分布检查等功能
 */

class AdaptiveManager {
    constructor() {
        this.isMonitoring = false;
        this.statusPollingInterval = null;
        this.currentThreshold = 0.1;
        this.lastStatus = null;
        
        this.initializeElements();
        this.bindEvents();
        this.loadInitialStatus();
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        this.elements = {
            startMonitorBtn: document.getElementById('start-monitor-btn'),
            stopMonitorBtn: document.getElementById('stop-monitor-btn'),
            manualTuneBtn: document.getElementById('manual-tune-btn'),
            checkDistributionBtn: document.getElementById('check-distribution-btn'),
            refreshStatusBtn: document.getElementById('refresh-status-btn'),
            refreshImagesBtn: document.getElementById('refresh-images-btn'),
            
            monitorStatus: document.getElementById('monitor-status'),
            fineTuneStatus: document.getElementById('fine-tune-status'),
            distributionInfo: document.getElementById('distribution-info'),
            
            thresholdSlider: document.getElementById('threshold-slider'),
            thresholdValue: document.getElementById('threshold-value'),
            
            oldGallery: document.getElementById('old-gallery'),
            newGallery: document.getElementById('new-gallery'),
            oldImagesInfo: document.getElementById('old-images-info'),
            newImagesInfo: document.getElementById('new-images-info')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 监控控制按钮
        if (this.elements.startMonitorBtn) {
            this.elements.startMonitorBtn.addEventListener('click', () => this.startAdaptiveMonitoring());
        }
        
        if (this.elements.stopMonitorBtn) {
            this.elements.stopMonitorBtn.addEventListener('click', () => this.stopAdaptiveMonitoring());
        }

        // 手动操作按钮
        if (this.elements.manualTuneBtn) {
            this.elements.manualTuneBtn.addEventListener('click', () => this.manualFineTune());
        }

        if (this.elements.checkDistributionBtn) {
            this.elements.checkDistributionBtn.addEventListener('click', () => this.checkDataDistribution());
        }

        if (this.elements.refreshStatusBtn) {
            this.elements.refreshStatusBtn.addEventListener('click', () => this.refreshStatus());
        }

        if (this.elements.refreshImagesBtn) {
            this.elements.refreshImagesBtn.addEventListener('click', () => this.refreshDatasetImages());
        }

        // 阈值滑块
        if (this.elements.thresholdSlider) {
            this.elements.thresholdSlider.addEventListener('input', (e) => this.handleThresholdChange(e));
        }
    }

    /**
     * 加载初始状态
     */
    async loadInitialStatus() {
        await this.refreshStatus();
        await this.loadDatasetComparison();
    }

    /**
     * 启动自适应监控
     */
    async startAdaptiveMonitoring() {
        try {
            const response = await API.startAdaptiveMonitoring();
            
            if (response.success) {
                this.isMonitoring = true;
                this.updateMonitoringUI();
                this.startStatusPolling();
                this.updateMonitorStatus('监控已启动，正在检测新数据...');
                showSuccess('自适应监控已启动');
            } else {
                throw new Error(response.message || '启动监控失败');
            }
        } catch (error) {
            console.error('启动监控失败:', error);
            ErrorHandler.handleAPIError(error, '启动自适应监控');
        }
    }

    /**
     * 停止自适应监控
     */
    async stopAdaptiveMonitoring() {
        try {
            const response = await API.stopAdaptiveMonitoring();
            
            if (response.success) {
                this.isMonitoring = false;
                this.updateMonitoringUI();
                this.stopStatusPolling();
                this.updateMonitorStatus('监控已停止');
                showSuccess('自适应监控已停止');
            } else {
                throw new Error(response.message || '停止监控失败');
            }
        } catch (error) {
            console.error('停止监控失败:', error);
            ErrorHandler.handleAPIError(error, '停止自适应监控');
        }
    }

    /**
     * 手动触发微调
     */
    async manualFineTune() {
        if (!confirm('确定要手动触发微调吗？这个过程可能需要较长时间。')) {
            return;
        }

        try {
            this.updateFineTuneStatus('正在启动手动微调...');
            
            const response = await API.manualFineTune();
            
            if (response.success) {
                this.updateFineTuneStatus('手动微调已启动，请等待完成...');
                showSuccess('手动微调已启动');
                
                // 开始轮询微调状态
                this.startStatusPolling();
            } else {
                throw new Error(response.message || '启动手动微调失败');
            }
        } catch (error) {
            console.error('手动微调失败:', error);
            ErrorHandler.handleAPIError(error, '手动微调');
            this.updateFineTuneStatus('手动微调启动失败');
        }
    }

    /**
     * 检查数据分布
     */
    async checkDataDistribution() {
        try {
            this.updateDistributionInfo('正在检查数据分布...');
            
            const response = await API.checkDataDistribution();
            
            if (response.success) {
                this.displayDistributionResults(response.distribution);
                showSuccess('数据分布检查完成');
            } else {
                throw new Error(response.message || '数据分布检查失败');
            }
        } catch (error) {
            console.error('数据分布检查失败:', error);
            ErrorHandler.handleAPIError(error, '数据分布检查');
            this.updateDistributionInfo('数据分布检查失败');
        }
    }

    /**
     * 刷新状态
     */
    async refreshStatus() {
        try {
            const response = await API.getAdaptiveStatus();
            
            if (response.success) {
                this.lastStatus = response.status;
                this.updateStatusDisplay(response.status);
            } else {
                throw new Error(response.message || '获取状态失败');
            }
        } catch (error) {
            console.error('刷新状态失败:', error);
            ErrorHandler.handleAPIError(error, '刷新状态');
        }
    }

    /**
     * 刷新数据集图片
     */
    async refreshDatasetImages() {
        try {
            await this.loadDatasetComparison(true);
            showSuccess('数据集图片已刷新');
        } catch (error) {
            console.error('刷新数据集图片失败:', error);
            ErrorHandler.handleAPIError(error, '刷新数据集图片');
        }
    }

    /**
     * 处理阈值变化
     */
    async handleThresholdChange(event) {
        const newThreshold = parseFloat(event.target.value);
        this.currentThreshold = newThreshold;
        
        // 更新显示
        if (this.elements.thresholdValue) {
            this.elements.thresholdValue.textContent = newThreshold.toFixed(2);
        }

        // 防抖更新后端
        clearTimeout(this.thresholdUpdateTimeout);
        this.thresholdUpdateTimeout = setTimeout(async () => {
            try {
                await API.updateDistributionThreshold(newThreshold);
                console.log('阈值已更新:', newThreshold);
            } catch (error) {
                console.error('更新阈值失败:', error);
                ErrorHandler.handleAPIError(error, '更新分布差异阈值');
            }
        }, 500);
    }

    /**
     * 更新监控UI状态
     */
    updateMonitoringUI() {
        if (this.elements.startMonitorBtn) {
            this.elements.startMonitorBtn.disabled = this.isMonitoring;
        }
        
        if (this.elements.stopMonitorBtn) {
            this.elements.stopMonitorBtn.disabled = !this.isMonitoring;
        }
    }

    /**
     * 开始状态轮询
     */
    startStatusPolling() {
        if (this.statusPollingInterval) {
            clearInterval(this.statusPollingInterval);
        }
        
        this.statusPollingInterval = setInterval(() => {
            this.refreshStatus();
        }, 5000); // 每5秒轮询一次
    }

    /**
     * 停止状态轮询
     */
    stopStatusPolling() {
        if (this.statusPollingInterval) {
            clearInterval(this.statusPollingInterval);
            this.statusPollingInterval = null;
        }
    }

    /**
     * 更新监控状态显示
     */
    updateMonitorStatus(message) {
        if (this.elements.monitorStatus) {
            this.elements.monitorStatus.value = `${new Date().toLocaleTimeString()}: ${message}`;
        }
    }

    /**
     * 更新微调状态显示
     */
    updateFineTuneStatus(message) {
        if (this.elements.fineTuneStatus) {
            this.elements.fineTuneStatus.value = `${new Date().toLocaleTimeString()}: ${message}`;
        }
    }

    /**
     * 更新分布信息显示
     */
    updateDistributionInfo(message) {
        if (this.elements.distributionInfo) {
            this.elements.distributionInfo.value = `${new Date().toLocaleTimeString()}: ${message}`;
        }
    }

    /**
     * 更新状态显示
     */
    updateStatusDisplay(status) {
        // 更新监控状态
        if (status.monitoring_status) {
            this.isMonitoring = status.monitoring_status.is_running;
            this.updateMonitoringUI();
            this.updateMonitorStatus(status.monitoring_status.message || '状态未知');
        }

        // 更新微调状态
        if (status.fine_tune_status) {
            this.updateFineTuneStatus(status.fine_tune_status.message || '状态未知');
        }

        // 更新分布信息
        if (status.distribution_check) {
            this.displayDistributionResults(status.distribution_check);
        }
    }

    /**
     * 显示分布检查结果
     */
    displayDistributionResults(distribution) {
        if (!distribution) return;

        let message = '';
        if (distribution.difference !== undefined) {
            message += `分布差异: ${distribution.difference.toFixed(4)}\n`;
            message += `阈值: ${distribution.threshold.toFixed(2)}\n`;
            message += `需要微调: ${distribution.needs_fine_tuning ? '是' : '否'}\n`;
        }
        
        if (distribution.details) {
            message += `详细信息: ${distribution.details}\n`;
        }
        
        if (distribution.timestamp) {
            message += `检查时间: ${new Date(distribution.timestamp).toLocaleString()}`;
        }

        this.updateDistributionInfo(message);
    }

    /**
     * 加载数据集对比
     */
    async loadDatasetComparison(forceRefresh = false) {
        try {
            let response;
            if (forceRefresh) {
                response = await API.refreshDatasetComparison(true);
            } else {
                response = await API.getDatasetComparison();
            }
            
            if (response.success) {
                this.displayDatasetComparison(response.comparison);
            } else {
                throw new Error(response.message || '获取数据集对比失败');
            }
        } catch (error) {
            console.error('加载数据集对比失败:', error);
            ErrorHandler.handleAPIError(error, '加载数据集对比');
        }
    }

    /**
     * 显示数据集对比
     */
    displayDatasetComparison(comparison) {
        // 显示旧数据集图片
        this.displayImageGallery(this.elements.oldGallery, comparison.old_images, 'old');
        this.displayDatasetInfo(this.elements.oldImagesInfo, comparison.old_dataset, comparison.old_images);

        // 显示新数据集图片
        this.displayImageGallery(this.elements.newGallery, comparison.new_images, 'new');
        this.displayDatasetInfo(this.elements.newImagesInfo, comparison.new_dataset, comparison.new_images);
    }

    /**
     * 显示图片画廊
     */
    displayImageGallery(container, images, datasetType) {
        if (!container) return;

        if (!images || images.length === 0) {
            container.innerHTML = `
                <div class="image-placeholder">
                    <i class="fas fa-image"></i>
                    <span>暂无${datasetType === 'old' ? '原始' : '新'}数据集图片</span>
                </div>
            `;
            return;
        }

        let galleryHTML = '';
        images.forEach(image => {
            const imageUrl = API.getDatasetImage(image.relative_path, true, 256);
            galleryHTML += `
                <div class="image-item">
                    <img src="${imageUrl}" alt="${image.filename}" loading="lazy">
                    <div class="image-label">${image.class_name}</div>
                </div>
            `;
        });

        container.innerHTML = galleryHTML;
    }

    /**
     * 显示数据集信息
     */
    displayDatasetInfo(container, dataset, images) {
        if (!container) return;

        let info = `数据集: ${dataset.name}\n`;
        info += `路径: ${dataset.path}\n`;
        info += `存在: ${dataset.exists ? '是' : '否'}\n`;
        info += `总类别数: ${dataset.total_classes}\n`;
        info += `总图片数: ${dataset.total_images}\n`;
        
        if (images && images.length > 0) {
            info += `\n当前显示图片:\n`;
            images.forEach((image, index) => {
                info += `${index + 1}. ${image.filename} (${image.class_name})\n`;
            });
        }

        container.value = info;
    }
}

// 创建全局自适应管理器实例
window.adaptiveManager = new AdaptiveManager();
