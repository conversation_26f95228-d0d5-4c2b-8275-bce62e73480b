#!/usr/bin/env python3
"""
遥感图像分类项目一键启动脚本

该脚本解决了模块导入路径问题，并提供一键启动FastAPI服务的功能。
支持开发模式和生产模式，自动检测环境并配置相应参数。
"""

import os
import sys
import subprocess
import argparse
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_python_path():
    """设置Python路径，确保模块能正确导入"""
    # 获取项目根目录
    project_root = Path(__file__).parent.absolute()
    
    # 将项目根目录添加到Python路径
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
        logger.info(f"已添加项目根目录到Python路径: {project_root}")
    
    # 将src目录添加到Python路径
    src_dir = project_root / "src"
    if str(src_dir) not in sys.path:
        sys.path.insert(0, str(src_dir))
        logger.info(f"已添加src目录到Python路径: {src_dir}")
    
    return project_root

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'torch',
        'torchvision',
        'pillow',
        'pydantic'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            logger.error(f"✗ {package} 未安装")
    
    if missing_packages:
        logger.error(f"缺少以下依赖包: {', '.join(missing_packages)}")
        logger.error("请运行以下命令安装依赖:")
        logger.error("pip install -r requirements.txt")
        return False
    
    return True

def check_project_structure(project_root):
    """检查项目结构是否完整"""
    required_paths = [
        "src/api/main.py",
        "src/api/dependencies.py",
        "src/api/routers",
        "static",
        "requirements.txt"
    ]
    
    missing_paths = []
    
    for path in required_paths:
        full_path = project_root / path
        if not full_path.exists():
            missing_paths.append(path)
            logger.error(f"✗ 缺少文件/目录: {path}")
        else:
            logger.info(f"✓ {path} 存在")
    
    if missing_paths:
        logger.error(f"项目结构不完整，缺少: {', '.join(missing_paths)}")
        return False
    
    return True

def create_directories(project_root):
    """创建必要的目录"""
    directories = [
        "imgs",
        "outputs",
        "outputs/checkpoints",
        "new_dataset",
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建目录: {directory}")
        else:
            logger.info(f"目录已存在: {directory}")

def start_server(host="0.0.0.0", port=8000, reload=False, workers=1):
    """启动FastAPI服务器"""
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = os.pathsep.join(sys.path)
        
        # 构建uvicorn命令
        cmd = [
            sys.executable, "-m", "uvicorn",
            "src.api.main:app",
            "--host", host,
            "--port", str(port),
            "--log-level", "info"
        ]
        
        if reload:
            cmd.append("--reload")
        else:
            cmd.extend(["--workers", str(workers)])
        
        logger.info(f"启动命令: {' '.join(cmd)}")
        logger.info(f"服务器将在 http://{host}:{port} 启动")
        logger.info(f"API文档: http://{host}:{port}/docs")
        logger.info(f"前端页面: http://{host}:{port}/static/index.html")
        
        # 启动服务器
        subprocess.run(cmd, env=env, check=True)
        
    except subprocess.CalledProcessError as e:
        logger.error(f"启动服务器失败: {e}")
        return False
    except KeyboardInterrupt:
        logger.info("服务器已停止")
        return True
    except Exception as e:
        logger.error(f"启动过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="遥感图像分类项目一键启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python start_server.py                    # 生产模式启动
  python start_server.py --dev             # 开发模式启动
  python start_server.py --host 127.0.0.1  # 指定主机地址
  python start_server.py --port 8080       # 指定端口
        """
    )
    
    parser.add_argument(
        "--host", 
        default="0.0.0.0", 
        help="服务器主机地址 (默认: 0.0.0.0)"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000, 
        help="服务器端口 (默认: 8000)"
    )
    parser.add_argument(
        "--dev", 
        action="store_true", 
        help="开发模式 (启用自动重载)"
    )
    parser.add_argument(
        "--workers", 
        type=int, 
        default=1, 
        help="工作进程数 (生产模式, 默认: 1)"
    )
    parser.add_argument(
        "--skip-checks", 
        action="store_true", 
        help="跳过依赖和结构检查"
    )
    
    args = parser.parse_args()
    
    logger.info("=" * 60)
    logger.info("遥感图像分类项目启动脚本")
    logger.info("=" * 60)
    
    # 设置Python路径
    project_root = setup_python_path()
    
    # 检查项目环境
    if not args.skip_checks:
        logger.info("检查项目环境...")
        
        if not check_dependencies():
            logger.error("依赖检查失败，请安装必要的依赖包")
            sys.exit(1)
        
        if not check_project_structure(project_root):
            logger.error("项目结构检查失败")
            sys.exit(1)
    
    # 创建必要目录
    create_directories(project_root)
    
    # 启动服务器
    logger.info("启动FastAPI服务器...")
    success = start_server(
        host=args.host,
        port=args.port,
        reload=args.dev,
        workers=args.workers
    )
    
    if success:
        logger.info("服务器已成功启动并运行")
    else:
        logger.error("服务器启动失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
