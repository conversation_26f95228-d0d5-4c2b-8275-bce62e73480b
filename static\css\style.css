/* 遥感场景分类项目 - 主样式文件 */
/* 现代化响应式设计 */

/* CSS变量定义 */
:root {
    /* 主色调 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;

    /* 次要色调 */
    --secondary-color: #64748b;
    --secondary-hover: #475569;
    --secondary-light: #f1f5f9;

    /* 成功/警告/错误色 */
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;

    /* 中性色 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* 字体 */
    --font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* 过渡 */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-800);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-light) 100%);
    min-height: 100vh;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 容器布局 */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 页面标题区域 */
.header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl) 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header .subtitle {
    font-size: 1.1rem;
    color: var(--gray-600);
    font-weight: 400;
    margin-bottom: var(--spacing-lg);
}

.status-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.6);
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    color: var(--gray-700);
}

.status-item i {
    color: var(--primary-color);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 标签页导航 */
.tab-nav {
    display: flex;
    background: var(--gray-100);
    padding: var(--spacing-sm);
    gap: var(--spacing-xs);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.tab-nav::-webkit-scrollbar {
    display: none;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: transparent;
    color: var(--gray-600);
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    min-width: fit-content;
    position: relative;
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.8);
    color: var(--gray-800);
    transform: translateY(-1px);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-md);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 3px;
    background: white;
    border-radius: var(--radius-sm);
}

.tab-btn i {
    font-size: 1rem;
}

/* 标签页内容 */
.tab-content {
    padding: var(--spacing-xl);
}

.tab-pane {
    display: none;
    animation: fadeIn var(--transition-normal);
}

.tab-pane.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 章节标题 */
.section-header {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--gray-200);
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.section-header h2 i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.section-header p {
    color: var(--gray-600);
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
}

.section-header .tip {
    background: var(--primary-light);
    border: 1px solid var(--primary-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    font-size: 0.9rem;
    color: var(--primary-color);
}

.section-header .tip i {
    margin-top: 2px;
    flex-shrink: 0;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn:not(:disabled):active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
}

.btn-secondary {
    background: var(--gray-600);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--gray-700);
}

.btn-success {
    background: var(--success-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-error {
    background: var(--error-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

/* 按钮组 */
.button-group {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
    margin-bottom: var(--spacing-lg);
}

/* 表单控件 */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    font-size: 0.9rem;
}

.form-control {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    transition: all var(--transition-fast);
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

select.form-control {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
    font-family: var(--font-mono);
    font-size: 0.85rem;
    line-height: 1.4;
}

/* 滑块控件 */
.slider-group {
    margin-bottom: var(--spacing-lg);
}

.slider-group label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    font-size: 0.9rem;
}

input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: var(--radius-sm);
    background: var(--gray-300);
    outline: none;
    -webkit-appearance: none;
    margin: var(--spacing-md) 0;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
}

input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-md);
}

/* 控制面板样式 */
.control-panel {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.control-section {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--gray-200);
}

.status-section {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--gray-200);
}

.control-group {
    margin-bottom: var(--spacing-xl);
}

.control-group:last-child {
    margin-bottom: 0;
}

.control-group h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.control-group h3 i {
    color: var(--primary-color);
}

.status-group {
    margin-bottom: var(--spacing-xl);
}

.status-group:last-child {
    margin-bottom: 0;
}

.status-group h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.config-group {
    margin-bottom: var(--spacing-xl);
}

.config-group:last-child {
    margin-bottom: 0;
}

.config-group h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 状态显示 */
.status-display {
    margin-bottom: var(--spacing-lg);
}

.status-display label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    font-size: 0.9rem;
}

.status-display textarea {
    width: 100%;
    min-height: 120px;
    padding: var(--spacing-md);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-family: var(--font-mono);
    font-size: 0.85rem;
    line-height: 1.4;
    background: var(--gray-100);
    color: var(--gray-700);
    resize: vertical;
    transition: all var(--transition-fast);
}

.status-display textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    background: white;
}

/* 信息显示区域 */
.image-info {
    margin-top: var(--spacing-lg);
}

.image-info label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    font-size: 0.9rem;
}

.image-info textarea {
    width: 100%;
    min-height: 80px;
    padding: var(--spacing-md);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-family: var(--font-mono);
    font-size: 0.85rem;
    line-height: 1.4;
    background: var(--gray-100);
    color: var(--gray-700);
    resize: vertical;
}

/* 阈值显示 */
#threshold-value {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: 600;
    font-size: 0.85rem;
    margin-left: var(--spacing-sm);
}

/* 数据集对比样式 */
.dataset-comparison {
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-2xl);
    border-top: 2px solid var(--gray-200);
}

.comparison-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.dataset-section {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--gray-200);
}

.dataset-section h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.dataset-section h3 i {
    color: var(--primary-color);
}

/* 图片画廊 */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    min-height: 300px;
}

.image-item {
    position: relative;
    background: white;
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
    border: 2px solid var(--gray-200);
}

.image-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.image-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    display: block;
}

.image-item .image-label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: var(--spacing-md) var(--spacing-sm);
    font-size: 0.8rem;
    font-weight: 500;
}

.image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 150px;
    background: var(--gray-200);
    color: var(--gray-500);
    font-size: 0.9rem;
    border-radius: var(--radius-md);
}

.image-placeholder i {
    margin-right: var(--spacing-sm);
    font-size: 1.2rem;
}

/* 模型预测样式 */
.prediction-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.input-section {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--gray-200);
}

.output-section {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--gray-200);
}

.upload-area {
    margin-bottom: var(--spacing-xl);
}

.upload-area h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.file-upload {
    position: relative;
}

.file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    background: white;
    cursor: pointer;
    transition: all var(--transition-fast);
    padding: var(--spacing-xl);
}

.upload-label:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.upload-label i {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: var(--spacing-md);
}

.upload-label:hover i {
    color: var(--primary-color);
}

.upload-label span {
    font-size: 1rem;
    color: var(--gray-600);
    font-weight: 500;
}

.image-preview {
    margin-top: var(--spacing-lg);
    text-align: center;
}

.image-preview img {
    max-width: 100%;
    max-height: 300px;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
}

/* 模型选择 */
.model-selection {
    margin-bottom: var(--spacing-xl);
}

.model-selection h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.model-selection select {
    width: 100%;
    margin-bottom: var(--spacing-lg);
}

/* 预测结果显示 */
.prediction-results {
    margin-bottom: var(--spacing-xl);
}

.prediction-results h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.result-display {
    background: white;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--gray-500);
    font-size: 1rem;
}

.no-result i {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
}

.prediction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
}

.prediction-item:last-child {
    border-bottom: none;
}

.prediction-label {
    font-weight: 500;
    color: var(--gray-800);
}

.prediction-confidence {
    font-weight: 600;
    color: var(--primary-color);
}

/* 推理时间显示 */
.inference-time h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.time-display {
    background: white;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* 评估标签页 */
.eval-tabs {
    margin-top: var(--spacing-lg);
}

.eval-tab-nav {
    display: flex;
    background: var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
    gap: var(--spacing-xs);
}

.eval-tab-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--gray-600);
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.eval-tab-btn:hover {
    background: rgba(255, 255, 255, 0.5);
    color: var(--gray-800);
}

.eval-tab-btn.active {
    background: white;
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.eval-tab-content {
    background: white;
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.eval-tab-pane {
    display: none;
    padding: var(--spacing-xl);
}

.eval-tab-pane.active {
    display: block;
}

/* 评估结果显示 */
.evaluation-results {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--gray-500);
    font-size: 1rem;
}

.loading i {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    background: white;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
}

.loading-spinner i {
    font-size: 3rem;
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

.loading-spinner span {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--gray-700);
}

/* 页脚 */
.footer {
    margin-top: var(--spacing-2xl);
    padding: var(--spacing-xl) 0;
    text-align: center;
    color: var(--gray-600);
    font-size: 0.9rem;
    border-top: 1px solid var(--gray-200);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .container {
        max-width: 100%;
        padding: var(--spacing-md);
    }

    .control-panel {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .prediction-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .comparison-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .header h1 {
        font-size: 2rem;
    }

    .header .subtitle {
        font-size: 1rem;
    }

    .tab-nav {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .tab-btn {
        flex: 1;
        min-width: calc(50% - var(--spacing-sm));
        text-align: center;
        justify-content: center;
    }

    .section-header h2 {
        font-size: 1.5rem;
    }

    .button-group {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .image-gallery {
        grid-template-columns: 1fr;
    }

    .eval-tab-nav {
        flex-direction: column;
    }

    .eval-tab-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .container {
        padding: var(--spacing-sm);
    }

    .header {
        padding: var(--spacing-lg) var(--spacing-md);
    }

    .header h1 {
        font-size: 1.8rem;
    }

    .tab-content {
        padding: var(--spacing-lg);
    }

    .control-section,
    .status-section,
    .input-section,
    .output-section,
    .dataset-section {
        padding: var(--spacing-lg);
    }

    .status-bar {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .tab-btn {
        min-width: 100%;
        margin-bottom: var(--spacing-xs);
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* 选择文本样式 */
::selection {
    background: var(--primary-light);
    color: var(--primary-color);
}

/* 焦点样式 */
:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 禁用状态 */
.disabled {
    opacity: 0.6;
    pointer-events: none;
}

/* 成功/错误状态 */
.success {
    color: var(--success-color);
}

.error {
    color: var(--error-color);
}

.warning {
    color: var(--warning-color);
}

/* 隐藏元素 */
.hidden {
    display: none !important;
}

/* 可见性切换 */
.invisible {
    visibility: hidden;
}

/* 文本对齐 */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

/* 提示消息样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    max-width: 500px;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 10000;
    animation: slideInRight var(--transition-normal);
    backdrop-filter: blur(10px);
}

.toast-success {
    background: rgba(5, 150, 105, 0.95);
    border: 1px solid var(--success-color);
    color: white;
}

.toast-error {
    background: rgba(220, 38, 38, 0.95);
    border: 1px solid var(--error-color);
    color: white;
}

.toast-warning {
    background: rgba(217, 119, 6, 0.95);
    border: 1px solid var(--warning-color);
    color: white;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.toast-content i {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.toast-content span {
    flex: 1;
    font-weight: 500;
}

.toast-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.toast-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 预测结果样式增强 */
.prediction-results-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.prediction-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-fast);
}

.prediction-item:hover {
    background: var(--gray-100);
    transform: translateX(2px);
}

.prediction-item.top-prediction {
    background: var(--primary-light);
    border-color: var(--primary-color);
    font-weight: 600;
}

.prediction-label {
    flex: 1;
    font-size: 1rem;
}

.prediction-confidence {
    font-weight: 600;
    color: var(--primary-color);
    margin-left: var(--spacing-md);
}

.prediction-bar {
    width: 100px;
    height: 6px;
    background: var(--gray-300);
    border-radius: var(--radius-sm);
    margin-left: var(--spacing-md);
    overflow: hidden;
}

.prediction-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
}

.prediction-error {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    color: var(--error-color);
    font-size: 1rem;
}

.prediction-error i {
    font-size: 1.5rem;
}

/* 初始化错误样式 */
.init-error {
    text-align: center;
    padding: var(--spacing-2xl);
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    margin: var(--spacing-xl);
}

.init-error h3 {
    color: var(--error-color);
    margin-bottom: var(--spacing-lg);
    font-size: 1.5rem;
}

.init-error p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

/* 错误状态样式 */
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-2xl);
    color: var(--error-color);
    text-align: center;
}

.error-state i {
    font-size: 3rem;
}

.no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-2xl);
    color: var(--gray-500);
    text-align: center;
}

.no-data i {
    font-size: 2rem;
}
