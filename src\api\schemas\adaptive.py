"""
自适应微调数据模型

定义自适应微调相关API的请求和响应数据模型，包括监控控制、分布检查、微调状态等。
与原有自适应微调函数的返回格式保持一致。
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime

from .common import SuccessResponse, StatusResponse


class AdaptiveStatus(BaseModel):
    """自适应监控状态模型"""
    
    monitoring_active: bool = Field(..., description="监控是否激活")
    last_check_time: Optional[datetime] = Field(default=None, description="上次检查时间")
    check_interval: int = Field(default=600, description="检查间隔(秒)")
    distribution_threshold: float = Field(default=0.1, ge=0, le=1, description="分布差异阈值")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
        schema_extra = {
            "example": {
                "monitoring_active": True,
                "last_check_time": "2023-12-01T12:00:00",
                "check_interval": 600,
                "distribution_threshold": 0.1
            }
        }


class AdaptiveStatusResponse(SuccessResponse):
    """自适应监控状态响应模型"""
    
    status: AdaptiveStatus = Field(..., description="监控状态信息")
    fine_tuning_status: Dict[str, Any] = Field(..., description="微调状态信息")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取状态成功",
                "status": {
                    "monitoring_active": True,
                    "last_check_time": "2023-12-01T12:00:00",
                    "check_interval": 600,
                    "distribution_threshold": 0.1
                },
                "fine_tuning_status": {
                    "running": False,
                    "message": "待机中"
                },
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class DistributionCheck(BaseModel):
    """数据分布检查模型"""
    
    old_dataset_path: str = Field(default="old_dataset", description="旧数据集路径")
    new_dataset_path: str = Field(default="new_dataset", description="新数据集路径")
    model_key: str = Field(default="resnet50-原始", description="用于特征提取的模型")
    num_samples: int = Field(default=1000, ge=100, le=10000, description="采样数量")
    
    class Config:
        schema_extra = {
            "example": {
                "old_dataset_path": "old_dataset",
                "new_dataset_path": "new_dataset",
                "model_key": "resnet50-原始",
                "num_samples": 1000
            }
        }


class DistributionCheckResult(BaseModel):
    """分布检查结果模型"""
    
    needs_fine_tuning: bool = Field(..., description="是否需要微调")
    difference_score: float = Field(..., ge=0, le=1, description="分布差异分数")
    threshold: float = Field(..., description="使用的阈值")
    old_samples_count: int = Field(..., description="旧数据样本数")
    new_samples_count: int = Field(..., description="新数据样本数")
    analysis_details: Dict[str, float] = Field(..., description="详细分析结果")
    
    class Config:
        schema_extra = {
            "example": {
                "needs_fine_tuning": True,
                "difference_score": 0.15,
                "threshold": 0.1,
                "old_samples_count": 1000,
                "new_samples_count": 856,
                "analysis_details": {
                    "mean_difference": 0.12,
                    "variance_difference": 0.08,
                    "mmd_approximation": 0.18
                }
            }
        }


class DistributionCheckResponse(SuccessResponse):
    """分布检查响应模型"""
    
    result: DistributionCheckResult = Field(..., description="检查结果")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "分布检查完成",
                "result": {
                    "needs_fine_tuning": True,
                    "difference_score": 0.15,
                    "threshold": 0.1,
                    "old_samples_count": 1000,
                    "new_samples_count": 856,
                    "analysis_details": {
                        "mean_difference": 0.12,
                        "variance_difference": 0.08,
                        "mmd_approximation": 0.18
                    }
                },
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class ThresholdUpdate(BaseModel):
    """阈值更新请求模型"""
    
    new_threshold: float = Field(..., ge=0, le=1, description="新的分布差异阈值")
    
    @validator('new_threshold')
    def validate_threshold(cls, v):
        if not 0 <= v <= 1:
            raise ValueError('阈值必须在0到1之间')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "new_threshold": 0.15
            }
        }


class ThresholdUpdateResponse(SuccessResponse):
    """阈值更新响应模型"""
    
    old_threshold: float = Field(..., description="旧阈值")
    new_threshold: float = Field(..., description="新阈值")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "阈值更新成功",
                "old_threshold": 0.1,
                "new_threshold": 0.15,
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class FineTuningRequest(BaseModel):
    """微调请求模型"""
    
    model_type: str = Field(default="resnet50", description="要微调的模型类型")
    epochs: int = Field(default=10, ge=1, le=100, description="微调轮数")
    learning_rate: float = Field(default=1e-5, gt=0, le=1, description="学习率")
    
    @validator('model_type')
    def validate_model_type(cls, v):
        allowed_types = ["densenet201", "resnet50", "swin_t", "vit_s_16"]
        if v not in allowed_types:
            raise ValueError(f'模型类型必须是以下之一: {allowed_types}')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "model_type": "resnet50",
                "epochs": 10,
                "learning_rate": 1e-5
            }
        }


class FineTuningStatus(BaseModel):
    """微调状态模型"""
    
    running: bool = Field(..., description="是否正在运行")
    message: str = Field(..., description="状态消息")
    progress: Optional[float] = Field(default=None, ge=0, le=100, description="进度百分比")
    current_epoch: Optional[int] = Field(default=None, description="当前轮次")
    total_epochs: Optional[int] = Field(default=None, description="总轮次")
    start_time: Optional[datetime] = Field(default=None, description="开始时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
        schema_extra = {
            "example": {
                "running": True,
                "message": "微调中... 轮次 3/10",
                "progress": 30.0,
                "current_epoch": 3,
                "total_epochs": 10,
                "start_time": "2023-12-01T12:00:00"
            }
        }


class ManualTuneResponse(SuccessResponse):
    """手动微调响应模型"""
    
    task_id: Optional[str] = Field(default=None, description="任务ID")
    estimated_duration: Optional[str] = Field(default=None, description="预估耗时")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "手动微调已启动",
                "task_id": "tune_20231201_120000",
                "estimated_duration": "约15-30分钟",
                "timestamp": "2023-12-01T12:00:00"
            }
        }


class MonitoringControlRequest(BaseModel):
    """监控控制请求模型"""
    
    action: str = Field(..., description="控制动作 (start, stop)")
    
    @validator('action')
    def validate_action(cls, v):
        if v not in ['start', 'stop']:
            raise ValueError('动作必须是 start 或 stop')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "action": "start"
            }
        }
