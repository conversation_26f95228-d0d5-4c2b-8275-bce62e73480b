# 原有忽略项
dependencies/
PatternNet.zip

# Git相关
.git/
.gitignore
.gitattributes

# Python相关
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.mypy_cache
.pytest_cache
.hypothesis

# 虚拟环境
venv/
ENV/
env/
.venv/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
.tmp/
.temp/

# 日志文件
logs/

# 测试相关
tests/
test_*
*_test.py

# 构建产物
build/
dist/
*.egg-info/

# Docker相关
docker-compose.yml
.dockerignore

# 开发环境配置
.env
.env.local
.env.development
.env.test
.env.production

# 备份文件
*.bak
*.backup