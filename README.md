# 🛰️ 遥感图像分类项目

本项目是一个现代化的遥感场景图像分类系统，采用**前后端分离架构**，支持多种深度学习模型和模型压缩技术。

## ✨ 项目特色

- 🏗️ **现代化架构**：FastAPI 后端 + HTML/CSS/JS 前端
- 🔄 **自适应微调**：智能监控新数据分布变化，自动触发模型微调
- 📊 **模型比较**：可视化展示不同模型和压缩技术的性能对比
- 🎨 **响应式设计**：现代化的 Web 界面，支持桌面和移动设备
- 🐳 **容器化部署**：Docker 支持，一键部署到生产环境
- 📡 **RESTful API**：完整的 API 服务，支持各种功能模块

## 📊 数据集说明

项目使用遥感场景数据集，包含 21 个类别：飞机、棒球场、篮球场、海滩、桥梁、灌木丛、密集住宅区、森林、高速公路、高尔夫球场、港口、十字路口、移动房屋公园、立交桥、停车场、铁路、河流、跑道、稀疏住宅区、储油罐、网球场。

数据集自动按照 7:1.5:1.5 的比例划分为训练集、验证集和测试集。

## 🏗️ 项目架构

```
📁 项目根目录/
├── 🔧 src/                    # 后端源代码
│   ├── 📁 api/                # FastAPI 应用
│   │   ├── 📁 core/           # 核心业务逻辑
│   │   ├── 📁 routers/        # API 路由
│   │   ├── 📁 schemas/        # 数据模型
│   │   └── main.py           # FastAPI 应用入口
│   ├── 📁 models/            # 深度学习模型定义
│   ├── 📁 optimization/      # 模型压缩技术
│   └── 📁 utils/             # 工具函数
├── 🎨 static/                # 前端静态文件
│   ├── 📁 css/               # 样式文件
│   ├── 📁 js/                # JavaScript 文件
│   └── 📄 index.html         # 主页面
├── 📁 outputs/               # 模型输出和检查点
├── 📁 old_dataset/           # 原始数据集
├── 📁 new_dataset/           # 新数据集（自适应微调）
├── 🐳 Dockerfile             # Docker 配置
├── 📋 requirements.txt       # 项目依赖
└── 📚 DEPLOYMENT.md          # 部署指南
```

## 🤖 支持的模型

| 模型类型        | 模型名称    | 来源        | 用途               |
| --------------- | ----------- | ----------- | ------------------ |
| **CNN**         | ResNet50    | torchvision | 主要分类模型       |
| **CNN**         | DenseNet201 | torchvision | 主要分类模型       |
| **Transformer** | ViT-S/16    | timm        | Vision Transformer |
| **Transformer** | Swin-T      | timm        | Swin Transformer   |
| **轻量化**      | MobileNetV2 | torchvision | 蒸馏学生模型       |
| **轻量化**      | ResNet18    | torchvision | 蒸馏学生模型       |

## 🚀 快速开始

### Docker 部署（推荐）

```bash
# 1. 构建镜像
docker build -t yaogan-classification .

# 2. 启动容器（GPU 版本）
docker run -d \
  --name yaogan-app \
  --gpus all \
  -p 8000:8000 \
  -v $(pwd)/outputs:/app/outputs \
  -v $(pwd)/imgs:/app/imgs \
  -v $(pwd)/new_dataset:/app/new_dataset \
  -v $(pwd)/static:/app/static \
  yaogan-classification

# 3. 访问应用
# 主页面: http://localhost:8000/static/index.html
# API 文档: http://localhost:8000/docs
```

### 超级快速启动（推荐新手）

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 一键启动和测试
python quick_start.py

# 服务器将自动启动并测试，然后显示访问地址
```

### 本地开发

#### 方法一：使用启动脚本（推荐）

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 使用启动脚本
python start_server.py --dev  # 开发模式（自动重载）
# 或
python start_server.py        # 生产模式

# 3. 访问应用
# 主页面: http://localhost:8000/static/index.html
# API 文档: http://localhost:8000/docs
# 健康检查: http://localhost:8000/health
```

#### 方法二：直接使用 uvicorn

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动 FastAPI 应用
python -m uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --reload

# 3. 访问应用
# 主页面: http://localhost:8000/static/index.html
# API 文档: http://localhost:8000/docs
```

#### 启动脚本选项

```bash
python start_server.py --help              # 查看所有选项
python start_server.py --dev               # 开发模式（自动重载）
python start_server.py --port 8080         # 指定端口
python start_server.py --host 127.0.0.1    # 指定主机
python start_server.py --skip-checks       # 跳过依赖检查（快速启动）
```

#### 测试 API 功能

```bash
# 测试API是否正常工作
python test_api.py

# 测试指定地址的API
python test_api.py --url http://localhost:8080

# 等待服务器启动后再测试
python test_api.py --wait
```

## 🌟 主要功能

### 1. 模型预测

- **多模型支持**：ResNet50、DenseNet201、ViT-S/16、Swin-T
- **实时预测**：上传图片即可获得分类结果
- **性能监控**：显示推理时间和置信度
- **历史记录**：保存预测历史便于回顾

### 2. 自适应微调

- **智能监控**：自动检测新数据分布变化
- **阈值控制**：可调节触发微调的敏感度
- **增量学习**：将新数据融入现有模型
- **可视化对比**：展示新旧数据集的差异

### 3. 模型比较

- **性能对比**：剪枝、蒸馏、量化效果对比
- **可视化展示**：HTML 表格展示详细指标
- **多维度分析**：准确率、模型大小、推理速度等

### 4. 数据集管理

- **图片浏览**：缩略图展示数据集内容
- **统计信息**：类别分布、文件大小等
- **安全访问**：防止路径遍历攻击

## 📚 API 文档

### 核心 API 端点

| 功能模块   | 端点                                   | 方法 | 描述             |
| ---------- | -------------------------------------- | ---- | ---------------- |
| **预测**   | `/api/prediction/predict`              | POST | 图片分类预测     |
| **预测**   | `/api/prediction/models`               | GET  | 获取可用模型列表 |
| **自适应** | `/api/adaptive/start-monitoring`       | POST | 启动自适应监控   |
| **自适应** | `/api/adaptive/stop-monitoring`        | POST | 停止自适应监控   |
| **自适应** | `/api/adaptive/status`                 | GET  | 获取监控状态     |
| **比较**   | `/api/comparison/pruning/{model}`      | GET  | 获取剪枝比较结果 |
| **比较**   | `/api/comparison/distillation/{model}` | GET  | 获取蒸馏比较结果 |
| **数据集** | `/api/dataset/info`                    | GET  | 获取数据集信息   |
| **数据集** | `/api/dataset/comparison`              | GET  | 获取数据集对比   |

### API 使用示例

```python
import requests

# 1. 模型预测
files = {'image': open('test.jpg', 'rb')}
data = {'model_name': 'resnet50'}
response = requests.post('http://localhost:8000/api/prediction/predict',
                        files=files, data=data)

# 2. 启动自适应监控
response = requests.post('http://localhost:8000/api/adaptive/start-monitoring')

# 3. 获取模型比较结果
response = requests.get('http://localhost:8000/api/comparison/pruning/resnet50')
```

详细的 API 文档可在应用启动后访问：`http://localhost:8000/docs`

## 🛠️ 技术栈

### 后端技术

- **FastAPI**: 现代化的 Python Web 框架
- **PyTorch**: 深度学习框架
- **timm**: 预训练模型库
- **Pydantic**: 数据验证和序列化
- **Pillow**: 图像处理库

### 前端技术

- **HTML5**: 语义化标记
- **CSS3**: 现代化样式设计
- **JavaScript (ES6+)**: 交互功能实现
- **Font Awesome**: 图标库

### 部署技术

- **Docker**: 容器化部署
- **NVIDIA Docker**: GPU 支持
- **uvicorn**: ASGI 服务器

## 🔧 开发指南

### 环境要求

- Python 3.8+
- CUDA 11.0+ (GPU 版本)
- Docker (可选)

### 本地开发设置

```bash
# 1. 克隆项目
git clone <repository-url>
cd yaogan-zsy

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 启动开发服务器
python start_server.py --dev
```

### 故障排除

#### 常见问题

1. **模块导入错误 (ModuleNotFoundError)**

   ```bash
   # 使用启动脚本可以自动解决路径问题
   python start_server.py --dev
   ```

2. **端口被占用**

   ```bash
   # 使用不同端口
   python start_server.py --port 8080
   ```

3. **依赖缺失**

   ```bash
   # 重新安装依赖
   pip install -r requirements.txt

   # 或跳过检查快速启动
   python start_server.py --skip-checks
   ```

4. **CUDA 相关错误**
   ```bash
   # 项目会自动检测并使用 CPU，无需特殊配置
   ```

## 🤝 贡献指南

我们欢迎各种形式的贡献！请遵循以下步骤：

1. **Fork** 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 **Pull Request**

### 代码规范

- 遵循 PEP 8 Python 代码规范
- 添加适当的注释和文档字符串
- 编写单元测试覆盖新功能
- 确保所有测试通过

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [PyTorch](https://pytorch.org/) - 深度学习框架
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化 Web 框架
- [timm](https://github.com/rwightman/pytorch-image-models) - 预训练模型库
- [Font Awesome](https://fontawesome.com/) - 图标库

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 Email: [<EMAIL>]
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/yaogan-zsy/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/your-username/yaogan-zsy/discussions)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
