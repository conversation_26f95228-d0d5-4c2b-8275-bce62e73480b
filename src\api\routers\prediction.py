"""
预测API路由

实现模型预测相关的API端点，包括图像上传、模型选择、预测执行等功能。
支持文件上传和模型列表查询，确保与原有predict函数保持一致的返回格式。
"""

import os
import sys
import time
import logging
from pathlib import Path
from typing import List, Optional
from PIL import Image
import io
import base64

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form
from fastapi.responses import JSONResponse

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入项目模块
from src.api.schemas.prediction import (
    ModelListResponse,
    PredictionRequest,
    PredictionResponse,
    PredictionResult,
    UploadImageResponse,
    ModelInfo,
    ModelLoadRequest,
    ModelLoadResponse,
)
from src.api.schemas.common import SuccessResponse, ErrorResponse
from src.api.dependencies import get_global_state, handle_api_error, verify_file_path
from src.api.core.image_processor import predict, save_uploaded_image
from src.api.core.model_manager import discover_available_models, load_model

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api", tags=["prediction"])

# 支持的图像格式
ALLOWED_IMAGE_EXTENSIONS = {".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB


def validate_image_file(file: UploadFile) -> None:
    """
    验证上传的图像文件

    Args:
        file: 上传的文件

    Raises:
        HTTPException: 文件验证失败时抛出异常
    """
    # 检查文件扩展名
    if file.filename:
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in ALLOWED_IMAGE_EXTENSIONS:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件格式。支持的格式: {', '.join(ALLOWED_IMAGE_EXTENSIONS)}",
            )

    # 检查文件大小
    if hasattr(file, "size") and file.size and file.size > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"文件大小超过限制。最大允许大小: {MAX_FILE_SIZE // (1024*1024)}MB",
        )


@router.get("/models", response_model=ModelListResponse)
async def get_available_models():
    """
    获取所有可用模型列表

    Returns:
        ModelListResponse: 包含模型列表的响应
    """
    try:
        logger.info("获取可用模型列表")

        # 获取全局状态
        state = get_global_state()

        # 确保模型已发现
        current_run_dir = state.get_current_run_dir()
        if not current_run_dir:
            # 尝试发现模型
            run_dir = discover_available_models()
            if not run_dir:
                return ModelListResponse(
                    success=True, message="未找到可用模型", models=[], total_count=0
                )

        # 获取模型路径信息
        model_paths = state.get_model_paths()

        # 构建模型信息列表
        models = []
        for model_key, model_info in model_paths.items():
            model_data = ModelInfo(
                key=model_key,
                name=f"{model_info['type'].upper()} {model_info['variant']}模型",
                type=model_info["type"],
                variant=model_info["variant"],
                device=str(model_info["device"]),
            )
            models.append(model_data)

        logger.info(f"找到 {len(models)} 个可用模型")

        return ModelListResponse(
            success=True,
            message=f"成功获取 {len(models)} 个可用模型",
            models=models,
            total_count=len(models),
        )

    except Exception as e:
        logger.error(f"获取模型列表失败: {str(e)}")
        http_exc = handle_api_error(e)
        raise http_exc


@router.get("/models/{model_key}/info", response_model=SuccessResponse)
async def get_model_info(model_key: str):
    """
    获取特定模型的详细信息

    Args:
        model_key: 模型键名

    Returns:
        SuccessResponse: 包含模型详细信息的响应
    """
    try:
        logger.info(f"获取模型信息: {model_key}")

        # 获取全局状态
        state = get_global_state()
        model_paths = state.get_model_paths()

        if model_key not in model_paths:
            raise HTTPException(status_code=404, detail=f"未找到模型: {model_key}")

        model_info = model_paths[model_key]

        # 获取模型文件信息
        model_path = model_info["path"]
        file_size_mb = 0
        if os.path.exists(model_path):
            file_size_mb = os.path.getsize(model_path) / (1024 * 1024)

        # 构建详细信息
        detailed_info = {
            "key": model_key,
            "name": f"{model_info['type'].upper()} {model_info['variant']}模型",
            "type": model_info["type"],
            "variant": model_info["variant"],
            "device": str(model_info["device"]),
            "path": model_path,
            "size_mb": round(file_size_mb, 2),
            "exists": os.path.exists(model_path),
        }

        return SuccessResponse(
            success=True, message=f"成功获取模型 {model_key} 的信息", data=detailed_info
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型信息失败: {str(e)}")
        http_exc = handle_api_error(e)
        raise http_exc


@router.post("/upload", response_model=UploadImageResponse)
async def upload_image(file: UploadFile = File(...)):
    """
    上传图像文件

    Args:
        file: 上传的图像文件

    Returns:
        UploadImageResponse: 上传结果响应
    """
    try:
        logger.info(f"上传图像文件: {file.filename}")

        # 验证文件
        validate_image_file(file)

        # 读取文件内容
        file_content = await file.read()

        # 验证是否为有效图像
        try:
            image = Image.open(io.BytesIO(file_content))
            image.verify()  # 验证图像完整性

            # 重新打开图像（verify后需要重新打开）
            image = Image.open(io.BytesIO(file_content))

        except Exception as e:
            raise HTTPException(status_code=400, detail=f"无效的图像文件: {str(e)}")

        # 保存图像
        saved_path = save_uploaded_image(image)

        # 构建响应
        return UploadImageResponse(
            success=True,
            message="图像上传成功",
            image_path=saved_path,
            image_url=f"/imgs/{os.path.basename(saved_path)}",
            file_size=len(file_content),
            image_format=image.format or "UNKNOWN",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图像上传失败: {str(e)}")
        http_exc = handle_api_error(e)
        raise http_exc


@router.post("/predict", response_model=PredictionResponse)
async def predict_image(model_key: str = Form(...), file: UploadFile = File(...)):
    """
    使用指定模型预测图像

    Args:
        model_key: 要使用的模型键名
        file: 上传的图像文件

    Returns:
        PredictionResponse: 预测结果响应
    """
    try:
        logger.info(f"开始预测: 模型={model_key}, 文件={file.filename}")

        # 验证文件
        validate_image_file(file)

        # 读取并验证图像
        file_content = await file.read()
        try:
            image = Image.open(io.BytesIO(file_content))
            image.verify()
            image = Image.open(io.BytesIO(file_content))
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"无效的图像文件: {str(e)}")

        # 验证模型是否存在
        state = get_global_state()
        model_paths = state.get_model_paths()

        if model_key not in model_paths:
            raise HTTPException(status_code=404, detail=f"未找到模型: {model_key}")

        # 执行预测
        start_time = time.time()
        predictions, time_info = predict(image, model_key)
        prediction_time = time.time() - start_time

        # 检查预测结果
        if isinstance(predictions, str):
            # 预测失败，返回错误信息
            raise HTTPException(status_code=500, detail=predictions)

        # 处理预测结果
        if not predictions:
            raise HTTPException(status_code=500, detail="预测结果为空")

        # 获取最高概率的预测
        top_class = max(predictions.keys(), key=lambda k: predictions[k])
        top_confidence = predictions[top_class]

        # 保存图像（可选）
        saved_path = None
        try:
            saved_path = save_uploaded_image(image)
        except Exception as e:
            logger.warning(f"保存图像失败: {str(e)}")

        # 构建预测结果
        result = PredictionResult(
            predictions=predictions,
            inference_time=time_info or f"推理时间: {prediction_time*1000:.2f} ms",
            model_used=model_key,
            top_prediction=top_class,
            confidence=float(top_confidence),
        )

        logger.info(f"预测完成: {top_class} ({top_confidence:.4f})")

        return PredictionResponse(
            success=True, message="预测完成", result=result, image_saved_path=saved_path
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预测失败: {str(e)}")
        http_exc = handle_api_error(e)
        raise http_exc


@router.post("/predict/base64", response_model=PredictionResponse)
async def predict_image_base64(request: PredictionRequest):
    """
    使用Base64编码的图像进行预测

    Args:
        request: 包含模型键名和Base64图像数据的请求

    Returns:
        PredictionResponse: 预测结果响应
    """
    try:
        logger.info(f"开始Base64预测: 模型={request.model_key}")

        if not request.image_data:
            raise HTTPException(status_code=400, detail="缺少图像数据")

        # 解析Base64图像数据
        try:
            # 处理data URL格式
            if request.image_data.startswith("data:"):
                # 提取Base64部分
                base64_data = request.image_data.split(",")[1]
            else:
                base64_data = request.image_data

            # 解码Base64
            image_bytes = base64.b64decode(base64_data)
            image = Image.open(io.BytesIO(image_bytes))

        except Exception as e:
            raise HTTPException(
                status_code=400, detail=f"无效的Base64图像数据: {str(e)}"
            )

        # 验证模型是否存在
        state = get_global_state()
        model_paths = state.get_model_paths()

        if request.model_key not in model_paths:
            raise HTTPException(
                status_code=404, detail=f"未找到模型: {request.model_key}"
            )

        # 执行预测
        start_time = time.time()
        predictions, time_info = predict(image, request.model_key)
        prediction_time = time.time() - start_time

        # 检查预测结果
        if isinstance(predictions, str):
            raise HTTPException(status_code=500, detail=predictions)

        if not predictions:
            raise HTTPException(status_code=500, detail="预测结果为空")

        # 获取最高概率的预测
        top_class = max(predictions.keys(), key=lambda k: predictions[k])
        top_confidence = predictions[top_class]

        # 保存图像（可选）
        saved_path = None
        try:
            saved_path = save_uploaded_image(image)
        except Exception as e:
            logger.warning(f"保存图像失败: {str(e)}")

        # 构建预测结果
        result = PredictionResult(
            predictions=predictions,
            inference_time=time_info or f"推理时间: {prediction_time*1000:.2f} ms",
            model_used=request.model_key,
            top_prediction=top_class,
            confidence=float(top_confidence),
        )

        logger.info(f"Base64预测完成: {top_class} ({top_confidence:.4f})")

        return PredictionResponse(
            success=True, message="预测完成", result=result, image_saved_path=saved_path
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Base64预测失败: {str(e)}")
        http_exc = handle_api_error(e)
        raise http_exc


@router.post("/models/load", response_model=ModelLoadResponse)
async def load_model_endpoint(request: ModelLoadRequest):
    """
    加载指定模型

    Args:
        request: 模型加载请求

    Returns:
        ModelLoadResponse: 加载结果响应
    """
    try:
        logger.info(f"加载模型: {request.model_key}")

        # 验证模型是否存在
        state = get_global_state()
        model_paths = state.get_model_paths()

        if request.model_key not in model_paths:
            raise HTTPException(
                status_code=404, detail=f"未找到模型: {request.model_key}"
            )

        # 如果强制重新加载，先卸载模型
        if request.force_reload:
            models = state.get_models()
            if request.model_key in models:
                del models[request.model_key]
                state.set_models(models)

        # 加载模型
        start_time = time.time()
        success = load_model(request.model_key)
        load_time = time.time() - start_time

        if not success:
            raise HTTPException(
                status_code=500, detail=f"模型加载失败: {request.model_key}"
            )

        logger.info(f"模型加载成功: {request.model_key}, 耗时: {load_time:.2f}秒")

        return ModelLoadResponse(
            success=True,
            message=f"模型 {request.model_key} 加载成功",
            model_key=request.model_key,
            load_time=round(load_time, 2),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"模型加载失败: {str(e)}")
        http_exc = handle_api_error(e)
        raise http_exc
